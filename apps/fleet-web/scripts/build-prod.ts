#!/usr/bin/env node

/* eslint-disable sonarjs/os-command, sonarjs/no-os-command-from-path */

import { execSync } from 'node:child_process'
import { resolve, dirname } from 'node:path'
import { fileURLToPath } from 'node:url'
import { z } from 'zod/v4'
import { Result } from 'neverthrow'

const safeExecSync = Result.fromThrowable(execSync, (err) => err as Error)

const scriptDir = dirname(fileURLToPath(import.meta.url))
const projectRoot = resolve(scriptDir, '../../../')

const buildCommand =
  'pnpm rimraf ./dist/apps/fleet-web && pnpm cross-env NODE_OPTIONS=--max-old-space-size=4144 NODE_ENV=production rspack build --config ./apps/fleet-web/webpack.config.js'

const envVars = z
  .object({
    POSTHOG_CLI_TOKEN: z.string().min(1),
    POSTHOG_CLI_ENV_ID: z.string().min(1),
  })
  .safeParse(process.env)

console.info('🏁 Starting production build...')

if (!envVars.success) {
  console.warn(
    '⚠️  Posthog environment variables not defined. Will not be able to upload source maps to posthog.',
  )
  console.warn(envVars.error.message)
  console.warn('-----------------------------------------------------')
}

const logFailedBuildAndExit = (error: Error) => {
  console.error('❌ Production build failed:', error)
  process.exit(1)
}

const buildResult = safeExecSync(buildCommand, {
  cwd: projectRoot,
  stdio: 'inherit',
})
if (buildResult.isErr()) {
  logFailedBuildAndExit(buildResult.error)
}

const injectResult = safeExecSync(
  'pnpm posthog-cli --host https://eu.posthog.com sourcemap inject --directory dist/apps/fleet-web',
  {
    cwd: projectRoot,
    stdio: 'inherit',
  },
)
if (injectResult.isErr()) {
  console.warn('⚠️  Failed to inject source maps into PostHog:', injectResult.error)
}

const uploadResult = safeExecSync(
  'pnpm posthog-cli --host https://eu.posthog.com sourcemap upload --directory dist/apps/fleet-web',
  {
    cwd: projectRoot,
    stdio: 'inherit',
  },
)
if (uploadResult.isErr()) {
  console.warn('⚠️  Failed to upload source maps to PostHog:', uploadResult.error)
}

if (injectResult.isOk() && uploadResult.isOk()) {
  const result = safeExecSync(
    'find dist/apps/fleet-web/static/js -name "*.js.map" -type f -delete',
    {
      cwd: projectRoot,
      stdio: 'inherit',
    },
  )
  if (result.isErr()) {
    console.warn('⚠️  Failed to delete source maps from dist:', result.error)
  } else {
    console.info('✅ Source maps deleted from dist/apps/fleet-web/static/js')
  }
} else {
  console.warn(
    '⚠️  Posthog source maps upload failed. Falling back to bundled source maps...',
  )
}

console.info('✅ Production build completed')
process.exit(0)

import { DateTime } from 'luxon'
import * as R from 'remeda'
import { z } from 'zod/v4'

import { messages } from 'src/shared/forms/messages'
import type { Path } from 'src/types/utils'
import { ctIntl } from 'src/util-components/ctIntl'
import {
  getMinAndMaxDateTimesForSpecifiedDateRangeConfig,
  isDateRangeValidAndWithinLimits,
} from 'src/util-functions/luxon/utils'

export const safeParseFromZodSchema = <
  DefaultValueResult,
  ValidationSchema extends z.ZodType,
>(
  validationSchema: ValidationSchema,
  input: unknown,
  {
    defaultValue,
  }: {
    defaultValue: (info: {
      input: unknown
      parseResultError: z.ZodError<any>
    }) => DefaultValueResult
  },
) => {
  type SchemaAfterValidation = z.infer<ValidationSchema>
  const parsedResult = validationSchema.safeParse(input)
  if (parsedResult.success) {
    return parsedResult.data as SchemaAfterValidation
  }
  return defaultValue({ input, parseResultError: parsedResult.error })
}

export function createZodDateTimeRangeSchema_edgesRequired({
  rangeMaxSizeInDays,
  outerLimits,
}: {
  rangeMaxSizeInDays?: number
  outerLimits?: { lower?: DateTime | undefined; upper?: DateTime | undefined }
}) {
  return z
    .custom<[DateTime, DateTime]>(
      (value) => {
        if (!R.isArray(value)) {
          return false
        }
        if (value.length !== 2) {
          return false
        }
        const [lower, upper] = value

        if (!DateTime.isDateTime(lower) || !DateTime.isDateTime(upper)) {
          return false
        }

        return true // basic validation passed
      },
      {
        /* Make sure to abort and not proceed with more validation.
           This is VERY IMPORTANT because following validations assume we already have a valid date range.
         */
        abort: true,
        error: ctIntl.formatMessage({ id: messages.required }),
      },
    )
    .refine(
      (val) => {
        if (rangeMaxSizeInDays) {
          const limits = getMinAndMaxDateTimesForSpecifiedDateRangeConfig(val, {
            rangeMaxSizeInDays,
            outerLimits,
          })

          return (
            isDateRangeValidAndWithinLimits({
              dateRange: val,
              limits,
            }).status === 'valid'
          )
        }
        return true
        // TODO Translate this message (talk to designers for a nice error message)
      },
      ctIntl.formatMessage(
        { id: 'dateTimeRange.helperText.withinLimits' },
        {
          values: { numberOfDays: rangeMaxSizeInDays },
        },
      ),
    )
}

export const createBaseEntityIdSchema = (errorMap?: { requiredErrorMsg: string }) =>
  z
    .union(
      [
        z
          .string({
            error: (issue) =>
              issue.code === 'invalid_type' ? errorMap?.requiredErrorMsg : undefined,
          })
          .refine(
            (val) => val !== 'undefined' && val !== 'null' && val !== '',
            errorMap?.requiredErrorMsg,
          ),
        z
          .number({
            error: (issue) =>
              issue.code === 'invalid_type' ? errorMap?.requiredErrorMsg : undefined,
          })
          .min(1, errorMap?.requiredErrorMsg),
      ],
      errorMap
        ? {
            error: () => ({ message: errorMap.requiredErrorMsg }),
          }
        : undefined,
    )
    .transform((value) => value.toString())

const createBrandedBaseEntityIdSchema = <const Brand extends string>(
  ...params: Parameters<typeof createBaseEntityIdSchema>
) =>
  createBaseEntityIdSchema(...params).brand<Brand>() as unknown as z.ZodType<
    string & z.core.$brand<Brand>,
    string & z.core.$brand<Brand>
  >

/**
 * string|number to cater for use cases where the BE returns a number instead of a string
 */
export const baseEntityIdSchema = createBaseEntityIdSchema()

/**
 *
 * Helper to create a branded entity id schema.
 *
 * Besides converting to a branded entity id, it also ensures that the input (z.input) is also branded.
 * This is __very important__ to ensure that using zod branded schemas with @tanstack/react-form works correctly.
 * context: https://github.com/TanStack/form/issues/1164
 */
export const brandedEntityIdSchema = <const Brand extends string>() => {
  const baseSchema = createBrandedBaseEntityIdSchema<Brand>()
  const brandedSchema = Object.assign(baseSchema, {
    asFormField: (
      ...params: Parameters<typeof createBrandedBaseEntityIdSchema<Brand>>
    ) => createBrandedBaseEntityIdSchema<Brand>(...params),
  })
  return brandedSchema
}

/**
 *
 * Helper to validate that we are passing a valid path.
 *
 * It's not as strict as it could be but it's good enough for now.
 */
export const createZodObjPathGetter = <SchemaObj>(_schemaObj: SchemaObj) => ({
  createPath: <const TPath extends Array<PropertyKey> & Path<SchemaObj>>(path: TPath) =>
    path as Array<string | number>,
})

/**
 *
 * Helper to validate that we are passing a valid path.
 *
 * It's not as strict as it could be but it's good enough for now.
 */
export const createZodArrayPathGetter = <SchemaObj>(
  _schemaArray: Array<SchemaObj>,
) => ({
  createPath: <const TPath extends Array<PropertyKey> & Path<SchemaObj>>(
    path: [number, ...TPath],
  ) => path as Array<string | number>,
})

export const createNumberAsStringSchema = (args?: {
  customParams: Parameters<typeof z.custom>[1]
}) =>
  z.custom<`${number}`>(
    (value): boolean => {
      if (typeof value !== 'string') {
        return false
      }
      if (value.trim().length === 0) {
        return false
      }
      return !Number.isNaN(Number(value))
    },
    args?.customParams,
  )

export const numberAsStringBasicSchema = createNumberAsStringSchema()

export const numberAsStringOrNumberTransformedSchema = numberAsStringBasicSchema
  .or(z.number())
  .transform(Number)

export const latLngSchema = z.object({
  lat: numberAsStringOrNumberTransformedSchema.refine((val) => val >= -90 && val <= 90),
  lng: numberAsStringOrNumberTransformedSchema.refine(
    (val) => val >= -180 && val <= 180,
  ),
})

export const createCartrackBooleanSchema = ({
  defaultValueWhenNotValid,
}: {
  defaultValueWhenNotValid: boolean
}) =>
  z
    .union([
      z.literal(true),
      z.literal(false),
      z.literal('t'),
      z.literal('f'),
      z.literal('true'),
      z.literal('false'),
    ])
    .catch(defaultValueWhenNotValid)
    .transform((val): boolean => {
      switch (val) {
        case 't':
        case 'true':
        case true: {
          return true
        }
        case 'f':
        case 'false':
        case false: {
          return false
        }
      }
    })

// Common schemas
export const generateStringSchema = (
  {
    isRequired = false,
    minLength,
    maxLength,
  }: {
    isRequired?: boolean
    minLength?: number
    maxLength?: number
  } = {},
  // For testing mock, we can pass a custom intl object
  intl: Pick<typeof ctIntl, 'formatMessage'> = ctIntl,
) => {
  let schema = z.string(
    isRequired
      ? {
          error: (issue) =>
            issue.code === 'invalid_type'
              ? /* so that it can work with fields that take null when cleared (Select, Autocomplete, etc.) */
                intl.formatMessage({ id: messages.required })
              : undefined,
        }
      : undefined,
  )

  if (isRequired) {
    schema = schema.min(1, messages.required)
  }

  if (minLength) {
    schema = schema.min(minLength, {
      message: intl.formatMessage(
        { id: 'formValidation.string.validMin' },
        { values: { number: minLength } },
      ),
    })
  }

  if (maxLength) {
    schema = schema.max(maxLength, {
      message: intl.formatMessage(
        { id: 'formValidation.string.validMax' },
        { values: { number: maxLength } },
      ),
    })
  }

  return schema
}

export const generateEmailSchema = ({
  isRequired = false,
}: { isRequired?: boolean } = {}) =>
  z.preprocess(
    (val) => (R.isNullish(val) ? '' : val),
    isRequired
      ? generateStringSchema({ isRequired: true }).email()
      : z.email().or(z.literal('')),
  ) as z.ZodPipe<
    z.ZodTransform<string, string>,
    z.ZodString | z.ZodUnion<[z.ZodEmail, z.ZodLiteral<''>]>
  >

import { ctIntl } from 'src/util-components/ctIntl'

import { renderLandmarkMarker } from 'cartrack-ui-kit'
import { useContextMenuDirections } from '../../shared/context-menu-utils/hooks'

export default function useGoogleContextMenuDirections() {
  const {
    directions,
    onDirectionsChange,
    handleDirectionsClick,
    renderDirectionsItems,
  } = useContextMenuDirections()

  const renderDirectionsMarkers = () => {
    const { start, end } = directions

    return [
      start &&
        renderLandmarkMarker({
          key: 'landmark-start',
          latInWgs84: start.lat,
          lngInWgs84: start.lng,
          name: ctIntl.formatMessage({ id: 'Directions Start' }),
          onLandmarkClick: handleDirectionsClick,
          color: 'green',
          scale: 1,
        }),
      end &&
        renderLandmarkMarker({
          key: 'landmark-end',
          latInWgs84: end.lat,
          lngInWgs84: end.lng,
          name: ctIntl.formatMessage({ id: 'Directions Finish' }),
          onLandmarkClick: handleDirectionsClick,
          color: 'red',
          scale: 1,
        }),
    ].filter(<PERSON><PERSON><PERSON>)
  }

  return {
    directions,
    onDirectionsChange,
    renderDirectionsMarkers,
    renderDirectionsItems,
  }
}

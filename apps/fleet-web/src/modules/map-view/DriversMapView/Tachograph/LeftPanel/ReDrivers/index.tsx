import { useEffect, useRef } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SearchTextField, Stack, Tooltip, Typography } from '@karoo-ui/core'
import FilterListIcon from '@mui/icons-material/FilterList'
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined'
import type { QueryStatus } from '@tanstack/react-query'
import { useDispatch } from 'react-redux'
import type { List } from 'react-virtualized'

import type { DriverId } from 'api/types'
import LeftPanel from 'src/components/_map/_panels/Left'
import LeftPanelBody from 'src/components/_map/_panels/Left/Body'
import LeftPanelHeader from 'src/components/_map/_panels/Left/Header'
import { useEffectEvent } from 'src/hooks/useEventHandler'
import { NoResults } from 'src/modules/map-view/shared/utils'
import { useTypedSelector } from 'src/redux-hooks'
import { variables } from 'src/shared/components/styled/global-styles'
import { GA4 } from 'src/shared/google-analytics4'
import { ctIntl } from 'src/util-components/ctIntl'

import {
  clickedFiltersButton,
  clickedSettingsButton,
  getReDriversMapViewLeftPanelSettings,
} from '../../ReDetailsPanel/slice'
import useDetailsPanelFiltersSettings from '../../ReDetailsPanel/useDetailsPanelFiltersSettings'
import { useTachographDriversMapViewContext } from '../../TachographDriversMapViewContext'
import DriversGroupedByVehicleList from '../List/Drivers/GroupedByVehicle'
import ReDriversList from '../List/ReDrivers/Default'
import useReFilteredDriversList from '../ReDrivers/useReFilteredDriversList'
import DriversLeftPanelLoader from './driversLoader'

type Props = {
  apiStatus: QueryStatus
}

const ReDriversLeftPanel = ({ apiStatus }: Props) => {
  const {
    data: { focusedDriverId },
    setData,
  } = useTachographDriversMapViewContext()

  const filteredDriversList = useReFilteredDriversList()
  const { detailsPanelState: settings, handleSearchTermChange } =
    useDetailsPanelFiltersSettings()

  const { isSettingsPanelOpen } = useTypedSelector(getReDriversMapViewLeftPanelSettings)

  const listRef = useRef<List>(null)

  const dispatch = useDispatch()

  const onFocusedDriverIdChange = useEffectEvent((driverId: string | null) => {
    if (!driverId) {
      return
    }
    const index = filteredDriversList.drivers.findIndex(
      (driver) => driver.id === driverId,
    )

    if (index !== -1) {
      const rowOffset = listRef.current?.getOffsetForRow({
        alignment: 'start',
        index,
      })

      if (rowOffset !== undefined) {
        listRef.current?.scrollToPosition(rowOffset)
      }
    }
  })

  useEffect(() => {
    onFocusedDriverIdChange(focusedDriverId)
  }, [focusedDriverId])

  const handleDriverClick = (id: DriverId) => {
    GA4.event({
      category: 'Map -> Drivers',
      action: 'Left Panel - Driver Click',
    })
    setData({ newFocusedDriverId: id })
  }

  return (
    <LeftPanel
      sx={{
        width: variables.redesignMapLeftPanelWidth,
      }}
    >
      <LeftPanelHeader>
        <SearchTextField
          fullWidth
          placeholder={ctIntl.formatMessage({ id: 'Search Drivers' })}
          value={settings.searchTerm}
          onClearIconClick={(_) => {
            handleSearchTermChange('')
          }}
          onChange={(e) => {
            GA4.eventWithTimeout({
              category: 'Map -> Drivers',
              action: 'Left Panel - Search Drivers',
            })
            handleSearchTermChange(e.target.value)
          }}
        />
      </LeftPanelHeader>
      <Stack
        direction="row"
        px={2}
        pt={2}
        pb={1}
        justifyContent="space-between"
      >
        <Stack
          direction="row"
          alignItems="center"
          gap={0.5}
        >
          <Typography variant="subtitle2">
            {filteredDriversList.drivers.length}
          </Typography>
          <Typography variant="caption">
            {ctIntl.formatMessage(
              { id: 'map.tachographDrivers.header.driversCount' },
              { values: { count: filteredDriversList.drivers.length } },
            )}
          </Typography>
        </Stack>
        <Stack
          direction="row"
          gap={0.5}
        >
          <Tooltip
            title={ctIntl.formatMessage({
              id: 'Filters',
            })}
            placement="bottom"
          >
            <IconButton
              onClick={() => {
                GA4.event({
                  category: 'Map -> Drivers',
                  action: 'Left Panel - Filters Button Click',
                })
                dispatch(clickedFiltersButton())
              }}
              sx={(theme) => ({
                bgcolor: isSettingsPanelOpen ? theme.palette.action.focus : 'inherit',
              })}
            >
              <FilterListIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip
            title={ctIntl.formatMessage({
              id: 'Settings',
            })}
            placement="bottom"
          >
            <IconButton
              onClick={() => {
                GA4.event({
                  category: 'Map -> Drivers',
                  action: 'Left Panel - Settings Button Click',
                })
                dispatch(clickedSettingsButton())
              }}
              sx={(theme) => ({
                bgcolor: isSettingsPanelOpen ? theme.palette.action.focus : 'inherit',
              })}
            >
              <SettingsOutlinedIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Stack>
      </Stack>
      <LeftPanelBody>
        {(() => {
          switch (apiStatus) {
            case 'pending': {
              return <DriversLeftPanelLoader />
            }
            case 'success': {
              // eslint-disable-next-line no-nested-ternary
              return filteredDriversList.drivers.length === 0 ? (
                <NoResults />
              ) : settings.groupDriversByVehicle ? (
                <DriversGroupedByVehicleList
                  driversGroupedByVehicle={filteredDriversList.driversGroupedByVehicle}
                  focusedDriverId={focusedDriverId}
                  vehicleDisplayName={settings.vehicleDisplayName}
                  onDriverClick={handleDriverClick}
                />
              ) : (
                <ReDriversList
                  drivers={filteredDriversList.drivers}
                  focusedDriverId={focusedDriverId}
                  vehicleDisplayName={settings.vehicleDisplayName}
                  onDriverClick={handleDriverClick}
                  ref={listRef}
                />
              )
            }
            default: {
              return null
            }
          }
        })()}
      </LeftPanelBody>
    </LeftPanel>
  )
}

export default ReDriversLeftPanel

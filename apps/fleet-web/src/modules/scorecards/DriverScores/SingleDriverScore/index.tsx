import { useMemo, useState } from 'react'
import {
  Box,
  Breadcrumbs,
  CircularProgressDelayedCentered,
  NativeLink,
  Stack,
  Typography,
  useTheme,
} from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { useHistory } from 'react-router'
import { match } from 'ts-pattern'

import type { DriverId } from 'api/types'
import { getDriversById } from 'duxs/drivers'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import { downloadHtmlElementByIdAsPdf } from 'src/util-functions/file-utils'

import { useFetchDriverScoreQuery, type FetchDriverScoreData } from '../api/queries'
import type { EventCategory } from '../api/types'
import DateRangeNavigator from '../components/DateRangeNavigator'
import { RiskEventDataGrid } from '../components/RiskEventDataGrid'
import { ScoreButtons } from '../components/ScoreButtons'
import { ScoreOverviewChart } from '../components/ScoreOverviewChart'
import {
  eventCategoryOptions,
  transformRiskEventsDataWithSingleGroup,
} from '../components/utils'
import {
  getSingleDriverPagePath,
  transformDriverScoreApiDataToChartData,
} from '../utils'

type Props = {
  driverId: string
  startDate: DateTime
  endDate: DateTime
}

export default function SingleDriverScore({
  driverId: dId,
  startDate,
  endDate,
}: Props) {
  const driverId = dId as DriverId
  const history = useHistory()
  const driverById = useTypedSelector(getDriversById)
  const driverName = driverById.get(driverId)?.name || ''

  const driverScoreQuery = useFetchDriverScoreQuery({
    driverId,
    startDate: startDate.toJSDate(),
    endDate: endDate.toJSDate(),
  })

  return (
    <Stack
      p={3}
      spacing={3}
      sx={{ overflowY: 'auto', width: '100%', height: '100%' }}
    >
      <Box
        display="grid"
        sx={{ gridTemplateColumns: '1fr 250px' }}
      >
        <Breadcrumbs>
          <NativeLink
            sx={{
              textDecoration: 'none',
              cursor: 'pointer',
              '&:hover': { textDecoration: 'underline' },
            }}
            onClick={() => {
              history.push(history.location.pathname)
            }}
          >
            {ctIntl.formatMessage({ id: 'Score' })}
          </NativeLink>
          <Typography>{driverName}</Typography>
        </Breadcrumbs>
        <DateRangeNavigator
          dateRangeOrShortcutId={[startDate, endDate]}
          onDateRangeChange={(newValue) => {
            if (newValue[0] && newValue[1]) {
              history.push(
                getSingleDriverPagePath(history.location, {
                  type: 'driver',
                  id: driverId,
                  start: newValue[0].startOf('day').toISO(),
                  end: newValue[1].endOf('day').toISO(),
                }),
              )
            }
          }}
        />
      </Box>
      <Box
        justifyContent="space-between"
        display="flex"
        alignItems="center"
      >
        <Box>
          <Box
            display="flex"
            gap={1}
            alignItems="center"
          >
            <Typography variant="h6">{driverName}</Typography>
            -
            <IntlTypography
              variant="h6"
              color="text.secondary"
              msgProps={{ id: 'scoreCards.driverScores.chart.scoreOverview' }}
            />
          </Box>

          <IntlTypography
            msgProps={{ id: 'scoreCards.driverScores.singleDriver.subtitle' }}
          />
        </Box>
        <ScoreButtons
          onExport={() => {
            downloadHtmlElementByIdAsPdf({
              elementId: 'Scorecard-single-driver-page',
              fileName: `Scorecard-${driverId}.pdf`,
              title: `${driverName}-${ctIntl.formatMessage({
                id: 'scoreCards.driverScores.chart.scoreOverview',
              })}`,
              displaySvg: true,
            })
          }}
        />
      </Box>

      <Stack
        spacing={3}
        id="Scorecard-single-driver-page"
        height="100%"
      >
        {match(driverScoreQuery)
          .with({ status: 'success' }, ({ data: apiData }) => (
            <SingleDriverScoreContent
              apiData={apiData}
              driverId={driverId}
              driverName={driverName}
              dateRange={[startDate, endDate]}
            />
          ))
          .with({ status: 'pending' }, () => <CircularProgressDelayedCentered />)
          .with({ status: 'error' }, () => null)
          .exhaustive()}
      </Stack>
    </Stack>
  )
}

const SingleDriverScoreContent = ({
  apiData,
  driverId,
  driverName,
  dateRange,
}: {
  apiData: FetchDriverScoreData
  driverId: DriverId
  driverName: string
  dateRange: [DateTime, DateTime]
}) => {
  const theme = useTheme()
  const [startDate, endDate] = dateRange
  const [eventCategoryFilter, setEventCategoryFilter] = useState<EventCategory>(
    eventCategoryOptions[0].value,
  )
  const { rowData, chartData } = useMemo(
    () => ({
      chartData: transformDriverScoreApiDataToChartData({
        apiData: apiData.periodScores[0],
        startDate: startDate.toJSDate(),
        endDate: endDate.toJSDate(),
        driver: {
          id: driverId,
          name: driverName,
          color: theme.palette.primary.main,
        },
        safetyScore: {
          color: theme.palette.info.main,
          value: apiData.safetyScore,
        },
      }),
      rowData:
        apiData.drivers.length > 0
          ? transformRiskEventsDataWithSingleGroup(apiData.drivers[0])
          : [],
    }),
    [
      apiData.drivers,
      apiData.periodScores,
      apiData.safetyScore,
      driverId,
      driverName,
      endDate,
      startDate,
      theme.palette.info.main,
      theme.palette.primary.main,
    ],
  )

  return (
    <>
      <ScoreOverviewChart data={chartData} />
      <RiskEventDataGrid
        dateRange={[startDate, endDate]}
        isSingleGroup
        eventCategoryFilter={eventCategoryFilter}
        setEventCategoryFilter={setEventCategoryFilter}
        rowData={rowData}
      />
    </>
  )
}

import { describe, it, vi } from 'vitest'
import { render } from '@testing-library/react'
import { DateTime } from 'luxon'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import { vExpect } from 'src/vitest/utils'

import DateRangeNavigator from './DateRangeNavigator'

// Mock the useDateRangeShortcutItems hook
vi.mock('src/hooks/useDateRangeShortcutItems', () => ({
  useDateRangeShortcutItems: () => ({
    last7Days: {
      id: 'last7Days',
      label: 'Last 7 days',
      getValue: () => [
        DateTime.local().minus({ days: 6 }).startOf('day'),
        DateTime.local().endOf('day'),
      ],
    },
    last30Days: {
      id: 'last30Days',
      label: 'Last 30 days',
      getValue: () => [
        DateTime.local().minus({ days: 29 }).startOf('day'),
        DateTime.local().endOf('day'),
      ],
    },
    last90Days: {
      id: 'last90Days',
      label: 'Last 90 days',
      getValue: () => [
        DateTime.local().minus({ days: 89 }).startOf('day'),
        DateTime.local().endOf('day'),
      ],
    },
    thisMonth: {
      id: 'thisMonth',
      label: 'This month',
      getValue: () => [
        DateTime.local().startOf('month'),
        DateTime.local().endOf('day'),
      ],
    },
    thisQuarter: {
      id: 'thisQuarter',
      label: 'This quarter',
      getValue: () => [
        DateTime.local().startOf('quarter'),
        DateTime.local().endOf('day'),
      ],
    },
  }),
}))

// Mock the ctIntl utility
vi.mock('src/util-components/ctIntl', () => ({
  ctIntl: {
    formatMessage: ({ id }: { id: string }) => id,
  },
}))

describe('DateRangeNavigator', () => {
  const mockOnDateRangeChange = vi.fn()
  const shortcuts = useDateRangeShortcutItems()

  it('should display the correct label when selectedOptionId is provided', () => {
    const { container } = render(
      <DateRangeNavigator
        dateRangeOrShortcutId={shortcuts.thisQuarter.id}
        onDateRangeChange={mockOnDateRangeChange}
      />,
    )

    // The component should display "This quarter" since we explicitly set the selectedOptionId
    vExpect(container.textContent?.includes('This quarter')).toBe(true)
  })

  it('should display the correct label when selectedOptionId is thisMonth', () => {
    const { container } = render(
      <DateRangeNavigator
        dateRangeOrShortcutId={shortcuts.thisMonth.id}
        onDateRangeChange={mockOnDateRangeChange}
      />,
    )

    // The component should display "This month" since we explicitly set the selectedOptionId
    vExpect(container.textContent?.includes('This month')).toBe(true)
  })

  it('should display custom date range when no selectedOptionId is provided', () => {
    const customStart = DateTime.local(2024, 7, 5) // July 5th, 2024
    const customEnd = DateTime.local(2024, 7, 10) // July 10th, 2024
    const dateRange: [DateTime, DateTime] = [customStart, customEnd]

    const { container } = render(
      <DateRangeNavigator
        dateRangeOrShortcutId={dateRange}
        onDateRangeChange={mockOnDateRangeChange}
      />,
    )

    // The component should display a custom date range format
    vExpect(container.textContent?.includes('Jul')).toBe(true)
  })
})

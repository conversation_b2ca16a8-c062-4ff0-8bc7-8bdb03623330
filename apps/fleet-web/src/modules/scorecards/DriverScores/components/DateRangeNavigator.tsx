import { useMemo, useState } from 'react'
import {
  Box,
  Chip,
  DateRangePicker,
  IconButton,
  Paper,
  SingleInputDateRangeField,
  Stack,
  Typography,
  type DateRange,
} from '@karoo-ui/core'
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined'
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
/* eslint-disable no-restricted-imports */
import type {
  DateRangeValidationError,
  PickerChangeHandlerContext,
} from '@mui/x-date-pickers-pro'
import type { DateTime } from 'luxon'

import type { CalendarShortcutId } from 'api/types'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import { ctIntl } from 'src/util-components/ctIntl'

type DateRangeOption = {
  id: CalendarShortcutId
  label: string
  getValue: () => DateRange<DateTime>
}

type DateRangeNavigatorProps = {
  dateRangeOrShortcutId: DateRange<DateTime> | CalendarShortcutId
  onDateRangeChange: (
    value: DateRange<DateTime>,
    context: PickerChangeHandlerContext<DateRangeValidationError>,
  ) => void
}

const CustomShortcuts = ({
  items,
  onChange,
  selectedOptionId,
}: {
  items: Array<DateRangeOption>
  onChange: (optionId: CalendarShortcutId) => void
  selectedOptionId: CalendarShortcutId | null
}) => (
  <Box
    sx={{
      gridCColumn: 1,
      gridRow: '2 / 3',
      p: 2,
    }}
  >
    <Stack gap={1.5}>
      {items.map((option) => {
        const isSelected = option.id === selectedOptionId

        return isSelected ? (
          <Chip
            key={option.id}
            label={option.label}
            onClick={() => onChange(option.id)}
            sx={{ justifyContent: 'flex-start' }}
          />
        ) : (
          <Typography
            key={option.id}
            variant="body2"
            sx={{
              cursor: 'pointer',
              '&:hover': {
                textDecoration: 'underline',
              },
              pl: 1,
            }}
            onClick={() => onChange(option.id)}
          >
            {option.label}
          </Typography>
        )
      })}
    </Stack>
  </Box>
)

const DateRangeNavigator = ({
  dateRangeOrShortcutId,
  onDateRangeChange,
}: DateRangeNavigatorProps) => {
  const shortcuts = useDateRangeShortcutItems()

  const dateRangeOptions = useMemo<Array<DateRangeOption>>(
    () => [
      shortcuts.last7Days,
      shortcuts.last30Days,
      shortcuts.last90Days,
      shortcuts.thisMonth,
      shortcuts.thisQuarter,
    ],
    [shortcuts],
  )

  const dateRange =
    typeof dateRangeOrShortcutId === 'string' && dateRangeOrShortcutId in shortcuts
      ? shortcuts[dateRangeOrShortcutId as keyof typeof shortcuts].getValue()
      : (dateRangeOrShortcutId as DateRange<DateTime>)

  const [selectedOptionId, setSelectedOptionId] = useState<CalendarShortcutId | null>(
    typeof dateRangeOrShortcutId === 'string' ? dateRangeOrShortcutId : null,
  )

  const selectedOptionIndex = useMemo(() => {
    if (selectedOptionId === null) {
      return null
    }
    return dateRangeOptions.findIndex((option) => option.id === selectedOptionId)
  }, [selectedOptionId, dateRangeOptions])

  const [isDateRangeModalOpen, setIsDateRangeModalOpen] = useState<boolean>(false)

  const handlePrevious = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    if (selectedOptionIndex === null) {
      // If currently on custom range, go to the last option
      const newIndex = dateRangeOptions.length - 1
      setSelectedOptionId(dateRangeOptions[newIndex].id)
      onDateRangeChange(dateRangeOptions[newIndex].getValue(), {
        validationError: [null, null],
      })
    } else {
      const newIndex =
        selectedOptionIndex > 0 ? selectedOptionIndex - 1 : dateRangeOptions.length - 1
      setSelectedOptionId(dateRangeOptions[newIndex].id)
      onDateRangeChange(dateRangeOptions[newIndex].getValue(), {
        validationError: [null, null],
      })
    }
  }

  const handleNext = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    if (selectedOptionIndex === null) {
      // If currently on custom range, go to the first option
      setSelectedOptionId(dateRangeOptions[0].id)
      onDateRangeChange(dateRangeOptions[0].getValue(), {
        validationError: [null, null],
      })
    } else {
      const newIndex =
        selectedOptionIndex < dateRangeOptions.length - 1 ? selectedOptionIndex + 1 : 0
      setSelectedOptionId(dateRangeOptions[newIndex].id)
      onDateRangeChange(dateRangeOptions[newIndex].getValue(), {
        validationError: [null, null],
      })
    }
  }

  const handleDateRangeChange = (
    newValue: DateRange<DateTime>,
    context: PickerChangeHandlerContext<DateRangeValidationError>,
  ) => {
    if (
      context.validationError === null ||
      (context.validationError[0] === null && context.validationError[1] === null)
    ) {
      onDateRangeChange(newValue, context)

      const matchingOption = dateRangeOptions.find((option) => {
        const optionRange = option.getValue()
        return (
          optionRange[0]?.toISODate() === newValue[0]?.toISODate() &&
          optionRange[1]?.toISODate() === newValue[1]?.toISODate()
        )
      })

      setSelectedOptionId(matchingOption?.id ?? null)
    }
  }

  const handleShortcutSelect = (optionId: CalendarShortcutId) => {
    const option = dateRangeOptions.find((opt) => opt.id === optionId)
    if (option) {
      setSelectedOptionId(optionId)
      onDateRangeChange(option.getValue(), { validationError: [null, null] })
    }
  }

  const currentLabel =
    selectedOptionIndex === null
      ? `${dateRange[0]?.toFormat('MMM dd')} - ${dateRange[1]?.toFormat('MMM dd')}`
      : dateRangeOptions[selectedOptionIndex].label

  return (
    <>
      <Paper
        variant="outlined"
        sx={{
          display: 'flex',
          alignItems: 'center',
          borderRadius: 1,
          cursor: 'pointer',
          height: '32px',
          width: '100%',
          pl: 0.5,
          pr: 0.5,
          justifyContent: 'space-between',
        }}
        onClick={() => setIsDateRangeModalOpen(true)}
      >
        <IconButton
          size="small"
          onClick={handlePrevious}
          sx={{
            borderRight: '1px solid',
            borderColor: 'divider',
            borderRadius: 0,
          }}
        >
          <ChevronLeftIcon fontSize="small" />
        </IconButton>

        <Typography
          sx={{
            px: 1,
            fontWeight: 'medium',
            textTransform: 'uppercase',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'text.secondary',
          }}
        >
          <CalendarTodayOutlinedIcon
            fontSize="small"
            sx={{ mr: 1 }}
          />
          {currentLabel}
        </Typography>

        <IconButton
          size="small"
          onClick={handleNext}
          sx={{
            borderLeft: '1px solid',
            borderColor: 'divider',
            borderRadius: 0,
          }}
        >
          <ChevronRightIcon fontSize="small" />
        </IconButton>
      </Paper>

      {isDateRangeModalOpen && (
        <div
          style={{
            position: 'absolute',
            visibility: 'hidden',
            height: 0,
            overflow: 'hidden',
          }}
        >
          <DateRangePicker
            slots={{
              field: SingleInputDateRangeField,
              shortcuts: (props) => (
                <CustomShortcuts
                  selectedOptionId={selectedOptionId}
                  {...props}
                  items={dateRangeOptions}
                  onChange={handleShortcutSelect}
                />
              ),
            }}
            localeText={{
              start: ctIntl.formatMessage({ id: 'Start Date' }),
              end: ctIntl.formatMessage({ id: 'End Date' }),
            }}
            slotProps={{
              textField: {
                sx: { opacity: 0, height: 0, width: 0, padding: 0, overflow: 'hidden' },
              },
              actionBar: { actions: ['accept'] },
              popper: {
                sx: {
                  position: 'fixed',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 1300,
                },
              },
            }}
            value={dateRange}
            onAccept={handleDateRangeChange}
            open
            onClose={() => setIsDateRangeModalOpen(false)}
            disableFuture
          />
        </div>
      )}
    </>
  )
}

export default DateRangeNavigator

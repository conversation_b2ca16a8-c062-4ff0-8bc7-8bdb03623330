import { useEffect, useRef } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  Divider,
  IconButton,
  Stack,
  TextField,
  Typography,
  type DateRange,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CloseIcon from '@mui/icons-material/Close'
import type { DateTime } from 'luxon'

import type { CalendarShortcutId } from 'api/types'
import { getVehicleTypeOptions } from 'duxs/vehicles'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import { MAX_COMPARISON_GROUPS } from '../event-handler'
import type { DriverScoresDispatch, DriverScoresState } from '../event-handler/types'
import DateRangeNavigator from './DateRangeNavigator'
import DriverVehicleGroupsPicker from './DriverVehicleGroupsPicker'

const GROUP_NAME_HEIGHT = 32

type GroupSelectionPanelProps = {
  dateRangeOrShortcutId: DateRange<DateTime> | CalendarShortcutId
  onDateRangeChange: (value: DateRange<DateTime>, context: any) => void
  groupState: DriverScoresState
  dispatch: DriverScoresDispatch
}

const GroupSelectionPanel = ({
  dateRangeOrShortcutId,
  onDateRangeChange,
  groupState,
  dispatch,
}: GroupSelectionPanelProps) => {
  const { comparisonGroups, editingComparisonGroupMeta } = groupState
  const inputRef = useRef<HTMLInputElement>(null)

  const vehicleTypeOptions = useTypedSelector(getVehicleTypeOptions)

  const editingGroupId = editingComparisonGroupMeta?.id

  useEffect(() => {
    if (editingGroupId && inputRef.current) {
      inputRef.current.focus()
    }
  }, [editingGroupId])

  const showDeleteButtons = comparisonGroups.length > 1

  return (
    <Box
      p={2}
      sx={{
        display: 'grid',
        gridTemplateRows: 'auto auto 1fr auto',
        backgroundColor: 'white',
        borderRadius: 2,
        height: '100%',
        gap: 2,
      }}
    >
      <Typography
        variant="subtitle2"
        fontWeight="500"
      >
        {ctIntl.formatMessage({
          id: 'scoreCards.driverScores.groupSelection.selectGroupsPrompt',
        })}
      </Typography>

      <Box>
        <DateRangeNavigator
          dateRangeOrShortcutId={dateRangeOrShortcutId}
          onDateRangeChange={onDateRangeChange}
        />
        <Divider sx={{ mt: 2 }} />
      </Box>

      <Box sx={{ overflowY: 'auto', minHeight: 0 }}>
        <Stack spacing={2}>
          {comparisonGroups.map((group) => (
            <Box
              key={group.id}
              sx={{ display: 'flex', flexDirection: 'column', mb: 2 }}
            >
              <Box
                sx={{
                  display: 'flex',
                  mb: 1,
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    flex: 1,
                    minWidth: 0,
                    overflow: 'hidden',
                  }}
                >
                  {editingGroupId !== group.id && (
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        bgcolor: group.color,
                        mr: 1,
                        flexShrink: 0,
                      }}
                    />
                  )}

                  <Box
                    sx={{
                      flex: 1,
                      minWidth: 0,
                      position: 'relative',
                    }}
                  >
                    {editingComparisonGroupMeta &&
                    editingComparisonGroupMeta.id === group.id ? (
                      <TextField
                        inputRef={inputRef}
                        value={editingComparisonGroupMeta.name}
                        onChange={(domEvent) =>
                          dispatch({
                            type: 'comparison_group_editing_name_textfield__onChange',
                            domEvent,
                          })
                        }
                        onBlur={(domEvent) =>
                          dispatch({
                            type: 'comparison_group_editing_name_textfield__onBlur',
                            domEvent,
                          })
                        }
                        onKeyDown={(domEvent) => {
                          dispatch({
                            type: 'comparison_group_editing_name_textfield__onKeyDown',
                            domEvent,
                          })
                        }}
                        size="small"
                        fullWidth
                        slotProps={{
                          input: {
                            startAdornment: (
                              <Box
                                sx={{
                                  width: 8,
                                  height: 8,
                                  borderRadius: '50%',
                                  bgcolor: group.color,
                                  mr: 1,
                                  flexShrink: 0,
                                }}
                              />
                            ),
                          },
                        }}
                        sx={{
                          '& .MuiInputBase-root': {
                            height: GROUP_NAME_HEIGHT,
                          },
                        }}
                        autoFocus
                      />
                    ) : (
                      <Typography
                        onClick={() =>
                          dispatch({
                            type: 'comparison_group_name__click',
                            groupId: group.id,
                          })
                        }
                        sx={{
                          cursor: 'pointer',
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          height: GROUP_NAME_HEIGHT,
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        {group.name}
                      </Typography>
                    )}
                  </Box>
                </Box>

                {showDeleteButtons && (
                  <IconButton
                    size="small"
                    onClick={() =>
                      dispatch({
                        type: 'comparison_group_remove_icon_button__onClick',
                        groupId: group.id,
                      })
                    }
                    sx={{
                      width: 16,
                      height: 16,
                      ml: 1,
                      flexShrink: 0,
                      bgcolor: 'grey.600',
                      color: 'white',
                      '&:hover': {
                        bgcolor: 'grey.700',
                      },
                    }}
                  >
                    <CloseIcon
                      fontSize="small"
                      sx={{ width: 12, height: 12 }}
                    />
                  </IconButton>
                )}
              </Box>

              <DriverVehicleGroupsPicker
                group={group}
                onVehicleDriverGroupIdsChange={({ vehicleGroupIds, driverGroupIds }) =>
                  dispatch({
                    type: 'driver_and_vehicle_groups_picker__onVehicleDriverGroupIdsChange',
                    payload: { id: group.id, vehicleGroupIds, driverGroupIds },
                  })
                }
              />

              <Box mt={1.5}>
                <Autocomplete
                  sx={{ width: '100%' }}
                  {...getAutocompleteVirtualizedProps({
                    options: vehicleTypeOptions,
                  })}
                  value={vehicleTypeOptions.find(
                    (t) => t.value === group.vehicleTypeId,
                  )}
                  onChange={(_event, option) =>
                    dispatch({
                      type: 'comparison_group_selected_type_autocomplete__onChange',
                      groupId: group.id,
                      option,
                    })
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={ctIntl.formatMessage({
                        id: 'scoreCards.driverScores.groupSelection.selectedType',
                      })}
                      placeholder={ctIntl.formatMessage({
                        id: 'scoreCards.driverScores.groupSelection.noVehicleTypeSelected',
                      })}
                      size="small"
                      slotProps={{ inputLabel: { shrink: true } }}
                    />
                  )}
                />
              </Box>
            </Box>
          ))}
        </Stack>
      </Box>

      <Box>
        <Divider sx={{ mb: 2 }} />
        <Button
          variant="text"
          startIcon={<AddIcon />}
          onClick={() => dispatch({ type: 'add_comparison_group_button__onClick' })}
          disabled={comparisonGroups.length >= MAX_COMPARISON_GROUPS}
          sx={{
            alignSelf: 'flex-start',
            color: 'primary.main',
            textTransform: 'uppercase',
          }}
        >
          {ctIntl.formatMessage({
            id: 'scoreCards.driverScores.groupSelection.addGroup',
          })}
        </Button>
      </Box>
    </Box>
  )
}

export default GroupSelectionPanel

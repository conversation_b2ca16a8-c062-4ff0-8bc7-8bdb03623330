import { describe, it } from 'vitest'
import { vExpect } from 'src/vitest/utils'

import {
  createWeightageConfigSchemaWithValidation,
  type WeightageConfigType,
} from './constants'

describe('scorecard constants', () => {
  describe('createWeightageConfigSchemaWithValidation', () => {
    const mockWeightageLevels = [
      {
        weightageLevelId: '1',
        value: 'low',
        threshold: 5,
      },
      {
        weightageLevelId: '2',
        value: 'medium',
        threshold: 10,
      },
      {
        weightageLevelId: '3',
        value: 'high',
        threshold: 15,
      },
      {
        weightageLevelId: '4',
        value: 'custom',
        threshold: null,
      },
    ]

    const createMockFormValues = (
      overrides: Partial<WeightageConfigType['form']> = {},
    ): WeightageConfigType => ({
      form: {
        speeding_over: { enabled: false, weightageId: '1', custom: 0 },
        speeding_10: { enabled: true, weightageId: '2', custom: 0 },
        speeding_20: { enabled: true, weightageId: '2', custom: 0 },
        speeding_30: { enabled: true, weightageId: '3', custom: 0 },
        speeding_limit: { enabled: false, weightageId: '1', custom: 0 },
        speeding_limit100: { enabled: false, weightageId: '1', custom: 0 },
        speeding_limit140: { enabled: false, weightageId: '1', custom: 0 },
        speeding_limit160: { enabled: false, weightageId: '1', custom: 0 },
        idling: { enabled: false, weightageId: '1', custom: 0 },
        harsh_turning: { enabled: false, weightageId: '1', custom: 0 },
        harsh_braking: { enabled: false, weightageId: '1', custom: 0 },
        harsh_acceleration: { enabled: false, weightageId: '1', custom: 0 },
        harsh_rpm: { enabled: false, weightageId: '1', custom: 0 },
        aiCamera_collision: { enabled: false, weightageId: '1', custom: 0 },
        aiCamera_smoking: { enabled: false, weightageId: '1', custom: 0 },
        aiCamera_cellPhone: { enabled: false, weightageId: '1', custom: 0 },
        aiCamera_distracted: { enabled: false, weightageId: '1', custom: 0 },
        aiCamera_fatigued: { enabled: false, weightageId: '1', custom: 0 },
        aiCamera_cameraCovered: { enabled: false, weightageId: '1', custom: 0 },
        aiCamera_seatbelt: { enabled: false, weightageId: '1', custom: 0 },
        aiCamera_forwardCollision: { enabled: false, weightageId: '1', custom: 0 },
        aiCamera_followingDistance: { enabled: false, weightageId: '1', custom: 0 },
        ...overrides,
      },
    })

    it('should validate successfully when progression is correct (increasing severity)', () => {
      const schema = createWeightageConfigSchemaWithValidation(mockWeightageLevels)
      const formValues = createMockFormValues({
        speeding_10: { enabled: true, weightageId: '1', custom: 0 }, // Low (5)
        speeding_20: { enabled: true, weightageId: '2', custom: 0 }, // Medium (10)
        speeding_30: { enabled: true, weightageId: '3', custom: 0 }, // High (15)
      })

      const result = schema.safeParse(formValues)
      vExpect(result.success).toBe(true)
    })

    it('should validate successfully when progression is equal (same severity)', () => {
      const schema = createWeightageConfigSchemaWithValidation(mockWeightageLevels)
      const formValues = createMockFormValues({
        speeding_10: { enabled: true, weightageId: '2', custom: 0 }, // Medium (10)
        speeding_20: { enabled: true, weightageId: '2', custom: 0 }, // Medium (10)
        speeding_30: { enabled: true, weightageId: '2', custom: 0 }, // Medium (10)
      })

      const result = schema.safeParse(formValues)
      vExpect(result.success).toBe(true)
    })

    it('should fail validation when higher speed has lower severity', () => {
      const schema = createWeightageConfigSchemaWithValidation(mockWeightageLevels)
      const formValues = createMockFormValues({
        speeding_10: { enabled: true, weightageId: '2', custom: 0 }, // Medium (10)
        speeding_20: { enabled: true, weightageId: '3', custom: 0 }, // High (15)
        speeding_30: { enabled: true, weightageId: '1', custom: 0 }, // Low (5) - Invalid!
      })

      const result = schema.safeParse(formValues)
      vExpect(result.success).toBe(false)
      if (!result.success) {
        vExpect(result.error.issues).toHaveLength(1)
        vExpect(result.error.issues[0].message).toBe(
          'Weightage/Speed score impact for higher speeding events cannot be lower than the previous.',
        )
        vExpect(result.error.issues[0].path).toEqual(['form', 'speeding_30'])
      }
    })

    it('should validate successfully when only one speeding event is enabled', () => {
      const schema = createWeightageConfigSchemaWithValidation(mockWeightageLevels)
      const formValues = createMockFormValues({
        speeding_10: { enabled: true, weightageId: '1', custom: 0 },
        speeding_20: { enabled: false, weightageId: '2', custom: 0 },
        speeding_30: { enabled: false, weightageId: '3', custom: 0 },
      })

      const result = schema.safeParse(formValues)
      vExpect(result.success).toBe(true)
    })

    it('should validate successfully when no speeding events are enabled', () => {
      const schema = createWeightageConfigSchemaWithValidation(mockWeightageLevels)
      const formValues = createMockFormValues({
        speeding_10: { enabled: false, weightageId: '1', custom: 0 },
        speeding_20: { enabled: false, weightageId: '2', custom: 0 },
        speeding_30: { enabled: false, weightageId: '3', custom: 0 },
      })

      const result = schema.safeParse(formValues)
      vExpect(result.success).toBe(true)
    })

    it('should handle custom weightage values correctly', () => {
      const schema = createWeightageConfigSchemaWithValidation(mockWeightageLevels)
      const formValues = createMockFormValues({
        speeding_10: { enabled: true, weightageId: '4', custom: 8 }, // Custom (8)
        speeding_20: { enabled: true, weightageId: '4', custom: 12 }, // Custom (12)
        speeding_30: { enabled: true, weightageId: '4', custom: 6 }, // Custom (6) - Invalid!
      })

      const result = schema.safeParse(formValues)
      vExpect(result.success).toBe(false)
      if (!result.success) {
        vExpect(result.error.issues).toHaveLength(1)
        vExpect(result.error.issues[0].message).toBe(
          'Weightage/Speed score impact for higher speeding events cannot be lower than the previous.',
        )
        vExpect(result.error.issues[0].path).toEqual(['form', 'speeding_30'])
      }
    })

    it('should validate against all previous events, not just immediate previous', () => {
      const schema = createWeightageConfigSchemaWithValidation(mockWeightageLevels)
      const formValues = createMockFormValues({
        speeding_10: { enabled: true, weightageId: '3', custom: 0 }, // High (15)
        speeding_20: { enabled: true, weightageId: '2', custom: 0 }, // Medium (10) - Invalid! Lower than speeding_10
        speeding_30: { enabled: true, weightageId: '3', custom: 0 }, // High (15) - Valid, equal to speeding_10
      })

      const result = schema.safeParse(formValues)
      vExpect(result.success).toBe(false)
      if (!result.success) {
        // Should have error for speeding_20 (lower than speeding_10)
        vExpect(result.error.issues.length).toBeGreaterThanOrEqual(1)

        // Check that speeding_20 has an error (lower than speeding_10)
        const speeding20Error = result.error.issues.find((issue) =>
          issue.path.includes('speeding_20'),
        )
        vExpect(speeding20Error).toBeDefined()
        vExpect(speeding20Error?.message).toBe(
          'Weightage/Speed score impact for higher speeding events cannot be lower than the previous.',
        )
      }
    })
  })
})

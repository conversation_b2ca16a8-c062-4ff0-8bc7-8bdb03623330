import { Box, Divider, Stack, Typography } from '@karoo-ui/core'

import { ctIntl } from 'src/util-components/ctIntl'

import type { WeightageScoreMap } from '../../utils'
import { ConfigurationPanelTitlePart } from '../components/ConfigurationPanel'

export const ScoreSummary = ({ scoresMap }: { scoresMap: WeightageScoreMap }) => (
  <Box
    sx={{
      borderRadius: '10px',
      backgroundColor: 'background.paper',
      overflowY: 'hidden',
      display: 'flex',
      flexDirection: 'column',
    }}
  >
    <Stack
      sx={{
        px: 3,
        pt: 3,
      }}
    >
      <ConfigurationPanelTitlePart
        title="scoreCards.settings.scoreSummary.title"
        subtitle="scoreCards.settings.scoreSummary.subtitle"
      />
      <Divider
        flexItem
        sx={{ my: 2 }}
      />
    </Stack>
    <Stack sx={{ overflowY: 'auto', px: 3, pb: 3 }}>
      {Object.entries(scoresMap)
        .sort((a, b) => Number(b[0]) - Number(a[0]))
        .map(([weightageId, data], idx) => (
          <Stack
            key={weightageId}
            spacing={1.5}
            sx={{ mb: 3 }}
          >
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="subtitle2">{data.label}</Typography>
              {idx === 0 && (
                <Typography variant="subtitle2">
                  {ctIntl.formatMessage({ id: 'Impact' })}
                </Typography>
              )}
            </Stack>
            {data.items.map((item) => (
              <Stack
                key={item.label}
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography color="text.secondary">{item.label}</Typography>
                <Typography
                  fontWeight="bold"
                  color="error.main"
                >
                  -{item.impact}
                </Typography>
              </Stack>
            ))}
          </Stack>
        ))}
    </Stack>
  </Box>
)

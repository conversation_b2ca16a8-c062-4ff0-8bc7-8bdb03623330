import { useMemo, useState } from 'react'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  accordionSummaryClasses,
  Box,
  colors,
  Divider,
  Slider,
  Stack,
  styled,
  Switch,
} from '@karoo-ui/core'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'

import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import FormSelection from 'src/util-components/selects/form-select'

import { MAX_SCORE, type WeightageValue } from '../../constants'
import { ConfigurationPanelTitlePart } from '../components/ConfigurationPanel'
import { useScorecardSettingsContext } from '../ScorecardSettingsContext'

type WeightageCardProps = {
  title: string
  subtitle: string
  isReadOnly: boolean
  fieldValue: WeightageValue // number for custom initial value
  onChange: (v: WeightageValue) => void
}

const WeightageCard = ({
  title,
  subtitle,
  fieldValue,
  isReadOnly,
  onChange,
}: WeightageCardProps) => {
  const {
    attributes: { weightageLevels },
  } = useScorecardSettingsContext()
  const [isExpanded, setIsExpanded] = useState(false)

  const weightageLevelOptions = useMemo(
    () =>
      weightageLevels.map((level) => {
        if (level.value.toLowerCase() === 'custom') {
          return {
            label: ctIntl.formatMessage(
              { id: 'scoreCards.settings.weightage.customOption' },
              { values: { custom: Math.max(0, fieldValue.custom) } },
            ),
            value: level.weightageLevelId,
          }
        }

        return { value: level.weightageLevelId, label: level.label }
      }),
    [fieldValue.custom, weightageLevels],
  )

  const customLevelId = useMemo(
    () => weightageLevels.find((o) => o.value === 'custom')?.weightageLevelId,
    [weightageLevels],
  )

  const selectedWeightageLevel = useMemo(
    () =>
      weightageLevels.find(
        (level) => level.weightageLevelId === fieldValue.weightageId,
      ),
    [fieldValue.weightageId, weightageLevels],
  )

  return (
    <Accordion
      expanded={isExpanded && fieldValue.enabled && !isReadOnly}
      elevation={0}
      sx={({ palette }) => ({
        width: '100%',
        border: `1px solid ${palette.divider}`,
        borderRadius: '10px !important',
        m: '0 !important',
        backgroundColor: 'transparent',
        '&:before': { display: 'none' }, // Remove Accordion's default top border
        '&.Mui-disabled': { backgroundColor: 'transparent' }, // Keep background transparent when disabled
      })}
      disabled={isReadOnly}
    >
      <AccordionSummary
        expandIcon={
          <ChevronRightIcon
            onClick={(event) => {
              event.stopPropagation() // Prevent AccordionSummary click from triggering Accordion expansion
              if (fieldValue.enabled) {
                setIsExpanded((prev) => !prev)
              }
            }}
          />
        }
        sx={{
          p: 0, // Remove default padding
          pr: 2,
          '& .MuiAccordionSummary-content': {
            margin: '0 !important',
            backgroundColor: 'transparent',
            display: 'grid',
            gridTemplateColumns: '50px 1fr 180px',
            alignItems: 'center',
            p: 3,
            gap: 1,
          }, // Remove default margin
          '&.Mui-disabled': { opacity: 1 }, // Prevent summary dimming when disabled
          [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]:
            { transform: 'rotate(90deg)' },
        }}
      >
        <Switch
          checked={fieldValue.enabled}
          onChange={(_, checked) => {
            onChange({ ...fieldValue, enabled: checked })
            if (!checked) {
              setIsExpanded(false) // Collapse accordion when disabled
            }
          }}
          disabled={isReadOnly}
        />
        <ConfigurationPanelTitlePart
          title={title}
          subtitle={subtitle}
        />
        <FormSelection
          testId={`${title}-weightage-select`}
          label="Weightage"
          value={fieldValue.weightageId}
          onChange={(v) => {
            onChange({ ...fieldValue, weightageId: v })
            if (v === customLevelId) {
              setIsExpanded(true)
            }
          }}
          options={weightageLevelOptions}
          disabled={!fieldValue.enabled || isReadOnly}
        />
      </AccordionSummary>
      <AccordionDetails
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 3,
          p: 3,
          pt: 0,
        }}
      >
        <Divider flexItem />
        {fieldValue.weightageId &&
          fieldValue.weightageId === customLevelId && ( // we show the slider when the custom is not -1
            <AccordionSummaryLine>
              <IntlTypography
                msgProps={{ id: 'scoreCards.settings.weightage.customOptionTitle' }}
                variant="subtitle2"
              />
              <Slider
                value={fieldValue.custom}
                onChange={(_event, newValue) => {
                  onChange({ ...fieldValue, custom: newValue })
                }}
                valueLabelDisplay="auto"
                min={0}
                max={MAX_SCORE}
                disabled={!fieldValue.enabled || isReadOnly}
                marks={[
                  { value: 0, label: 0 },
                  { value: MAX_SCORE, label: MAX_SCORE },
                ]}
              />
            </AccordionSummaryLine>
          )}
        <AccordionSummaryLine>
          <IntlTypography
            msgProps={{ id: 'scoreCards.settings.weightage.scorePreview' }}
            variant="subtitle2"
          />
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box
              component="span"
              sx={(theme) => ({
                width: '40px',
                height: '30px',
                borderRadius: '4px',
                backgroundColor: colors.red[50],
                color: 'error.main',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: theme.typography.fontWeightMedium,
              })}
            >
              -{selectedWeightageLevel?.threshold ?? fieldValue.custom}
            </Box>
            <IntlTypography
              msgProps={{ id: 'scoreCards.settings.weightage.scoreExplanation' }}
              variant="caption"
            />
          </Box>
        </AccordionSummaryLine>
      </AccordionDetails>
    </Accordion>
  )
}

const AccordionSummaryLine = styled(Stack)({
  flexDirection: 'row',
  alignItems: 'center',
  width: '100%',
  display: 'grid',
  gridTemplateColumns: '200px 1fr',
})

export default WeightageCard

import { useCallback, useMemo, useState } from 'react'
import { KarooFormStateContextProvider, Stack, styled } from '@karoo-ui/core'
import { useForm, useStore } from '@tanstack/react-form'

import {
  createWeightageConfigSchemaWithValidation,
  type WeightageConfigType,
  type WeightageValue,
} from '../../constants'
import { generateScoresMap } from '../../utils'
import { useUpdateScorecardWeightageMutation } from '../api/mutation'
import type { FetchScorecardConfigurationWeightageData } from '../api/queries'
import { ConfigurationPanelTitlePart } from '../components/ConfigurationPanel'
import EditButtonGroup from '../components/EditButtonGroup'
import { useScorecardSettingsContext } from '../ScorecardSettingsContext'
import { formatWeightageForApi, getWeightageChangedFields } from '../utils'
import { ScoreSummary } from './ScoreSummary'
import WeightageCard from './WeightageCard'

export const Weightage = ({
  weightageData,
}: {
  weightageData: FetchScorecardConfigurationWeightageData['weightageData']
}) => {
  const {
    attributes: { weightageLevels },
    defaultValues: { weightageRules: defaultWeightageRules },
  } = useScorecardSettingsContext()
  const [isEditing, setIsEditing] = useState(false)
  const updateWeightageMutation = useUpdateScorecardWeightageMutation()

  const verifyWeightageValue = useCallback(
    (weightageValue: WeightageValue) => ({
      ...weightageValue,
      weightageId: weightageLevels
        .map((level) => level.weightageLevelId)
        .includes(weightageValue.weightageId)
        ? weightageValue.weightageId
        : weightageLevels[0].weightageLevelId,
    }),
    [weightageLevels],
  )

  const parsedWeightageData = useMemo(
    () => ({
      form: {
        speeding_over: verifyWeightageValue(weightageData.speeding_over),
        speeding_10: verifyWeightageValue(weightageData.speeding_10),
        speeding_20: verifyWeightageValue(weightageData.speeding_20),
        speeding_30: verifyWeightageValue(weightageData.speeding_30),
        speeding_limit: verifyWeightageValue(weightageData.speeding_limit),
        speeding_limit100: verifyWeightageValue(weightageData.speeding_limit100),
        speeding_limit140: verifyWeightageValue(weightageData.speeding_limit140),
        speeding_limit160: verifyWeightageValue(weightageData.speeding_limit160),
        harsh_turning: verifyWeightageValue(weightageData.harsh_turning),
        harsh_braking: verifyWeightageValue(weightageData.harsh_braking),
        harsh_acceleration: verifyWeightageValue(weightageData.harsh_acceleration),
        harsh_rpm: verifyWeightageValue(weightageData.harsh_rpm),
        idling: verifyWeightageValue(weightageData.idling),
        aiCamera_collision: verifyWeightageValue(weightageData.aiCamera_collision),
        aiCamera_smoking: verifyWeightageValue(weightageData.aiCamera_smoking),
        aiCamera_cellPhone: verifyWeightageValue(weightageData.aiCamera_cellPhone),
        aiCamera_distracted: verifyWeightageValue(weightageData.aiCamera_distracted),
        aiCamera_fatigued: verifyWeightageValue(weightageData.aiCamera_fatigued),
        aiCamera_cameraCovered: verifyWeightageValue(
          weightageData.aiCamera_cameraCovered,
        ),
        aiCamera_seatbelt: verifyWeightageValue(weightageData.aiCamera_seatbelt),
        aiCamera_forwardCollision: verifyWeightageValue(
          weightageData.aiCamera_forwardCollision,
        ),
        aiCamera_followingDistance: verifyWeightageValue(
          weightageData.aiCamera_followingDistance,
        ),
      },
    }),
    [verifyWeightageValue, weightageData],
  )

  // Create schema with validation based on weightage levels
  const validationSchema = useMemo(
    () => createWeightageConfigSchemaWithValidation(weightageLevels),
    [weightageLevels],
  )

  const form = useForm({
    validators: { onChange: validationSchema },
    defaultValues: parsedWeightageData as WeightageConfigType,
    onSubmit: ({ value }) => {
      const changed = getWeightageChangedFields(value.form, parsedWeightageData.form)
      const apiInput = formatWeightageForApi(changed)
      updateWeightageMutation.mutate(apiInput, {
        onSuccess: () => {
          setIsEditing(false) // Exit editing mode on successful save
        },
      })
    },
  })

  const {
    isValid,
    isDirty,
    values: formValues,
  } = useStore(form.store, (state) => ({
    isValid: state.isValid,
    isDirty: state.isDirty,
    values: state.values,
  }))

  const scoresMap = useMemo(
    () => generateScoresMap({ formValues, weightageLevels }),
    [formValues, weightageLevels],
  )

  return (
    <KarooFormStateContextProvider value={{ readOnly: !isEditing }}>
      <Stack sx={{ padding: 1.5, gap: 2, overflowY: 'hidden' }}>
        <EditButtonGroup
          isEditing={isEditing}
          saveButtonDisabled={!isValid || !isDirty}
          isSaving={updateWeightageMutation.isPending}
          onCancelButtonClick={() => {
            form.reset(parsedWeightageData) // Reset form on cancel
            setIsEditing(false)
          }}
          onResetButtonClick={() => {
            form.setFieldValue('form', defaultWeightageRules)
          }}
          onSaveButtonClick={form.handleSubmit}
          onEditButtonClick={() => setIsEditing(true)}
        />

        <Stack
          sx={{
            display: 'grid',
            gridTemplateColumns: '1fr 300px',
            overflowY: 'hidden',
            gap: 2,
          }}
        >
          <Stack sx={{ overflowY: 'auto', gap: 2 }}>
            <Section>
              <ConfigurationPanelTitlePart
                title="scoreCards.settings.weightage.speeding.title"
                subtitle="scoreCards.settings.weightage.speeding.subtitle"
              />
              <form.Field name="form.speeding_over">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.speeding.over.title"
                    subtitle="scoreCards.settings.weightage.speeding.over.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.speeding_10">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.speeding.over10.title"
                    subtitle="scoreCards.settings.weightage.speeding.over10.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.speeding_20">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.speeding.over20.title"
                    subtitle="scoreCards.settings.weightage.speeding.over20.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.speeding_30">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.speeding.over30.title"
                    subtitle="scoreCards.settings.weightage.speeding.over30.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.speeding_limit">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.speeding.overLimit.title"
                    subtitle="scoreCards.settings.weightage.speeding.overLimit.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.speeding_limit100">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.speeding.overLimit100.title"
                    subtitle="scoreCards.settings.weightage.speeding.overLimit100.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.speeding_limit140">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.speeding.overLimit140.title"
                    subtitle="scoreCards.settings.weightage.speeding.overLimit140.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.speeding_limit160">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.speeding.overLimit160.title"
                    subtitle="scoreCards.settings.weightage.speeding.overLimit160.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
            </Section>

            <Section>
              <ConfigurationPanelTitlePart
                title="scoreCards.settings.weightage.harsh.title"
                subtitle="scoreCards.settings.weightage.harsh.subtitle"
              />
              <form.Field name="form.harsh_acceleration">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.harsh.acceleration.title"
                    subtitle="scoreCards.settings.weightage.harsh.acceleration.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.harsh_braking">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.harsh.braking.title"
                    subtitle="scoreCards.settings.weightage.harsh.braking.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.harsh_turning">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.harsh.turning.title"
                    subtitle="scoreCards.settings.weightage.harsh.turning.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.harsh_rpm">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.harsh.rpm.title"
                    subtitle="scoreCards.settings.weightage.harsh.rpm.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
            </Section>

            <Section>
              <ConfigurationPanelTitlePart
                title="scoreCards.settings.weightage.driverBehaviour.title"
                subtitle="scoreCards.settings.weightage.driverBehaviour.subtitle"
              />
              <form.Field name="form.idling">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.driverBehaviour.idling.title"
                    subtitle="scoreCards.settings.weightage.driverBehaviour.idling.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
            </Section>

            <Section>
              <ConfigurationPanelTitlePart
                title="scoreCards.settings.weightage.aiCamera.title"
                subtitle="scoreCards.settings.weightage.aiCamera.subtitle"
              />
              <form.Field name="form.aiCamera_cellPhone">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.aiCamera.cellPhone.title"
                    subtitle="scoreCards.settings.weightage.aiCamera.cellPhone.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.aiCamera_fatigued">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.aiCamera.fatigued.title"
                    subtitle="scoreCards.settings.weightage.aiCamera.fatigued.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.aiCamera_distracted">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.aiCamera.distracted.title"
                    subtitle="scoreCards.settings.weightage.aiCamera.distracted.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.aiCamera_cameraCovered">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.aiCamera.cameraCovered.title"
                    subtitle="scoreCards.settings.weightage.aiCamera.cameraCovered.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.aiCamera_smoking">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.aiCamera.smoking.title"
                    subtitle="scoreCards.settings.weightage.aiCamera.smoking.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.aiCamera_seatbelt">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.aiCamera.seatbelt.title"
                    subtitle="scoreCards.settings.weightage.aiCamera.seatbelt.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
            </Section>

            <Section>
              <ConfigurationPanelTitlePart
                title="scoreCards.settings.weightage.aiCollision.title"
                subtitle="scoreCards.settings.weightage.aiCollision.subtitle"
              />
              <form.Field name="form.aiCamera_forwardCollision">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.aiCamera.forwardCollision.title"
                    subtitle="scoreCards.settings.weightage.aiCamera.forwardCollision.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.aiCamera_followingDistance">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.aiCamera.followingDistance.title"
                    subtitle="scoreCards.settings.weightage.aiCamera.followingDistance.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
              <form.Field name="form.aiCamera_collision">
                {(field) => (
                  <WeightageCard
                    title="scoreCards.settings.weightage.aiCamera.collision.title"
                    subtitle="scoreCards.settings.weightage.aiCamera.collision.subtitle"
                    isReadOnly={!isEditing}
                    fieldValue={field.state.value}
                    onChange={field.handleChange}
                  />
                )}
              </form.Field>
            </Section>
          </Stack>
          <ScoreSummary scoresMap={scoresMap} />
        </Stack>
      </Stack>
    </KarooFormStateContextProvider>
  )
}

const Section = styled(Stack)(({ theme }) => ({
  borderRadius: '10px',
  backgroundColor: theme.palette.background.paper,
  padding: theme.spacing(2),
  paddingTop: theme.spacing(3),
  paddingBottom: theme.spacing(3),
  display: 'flex',
  gap: theme.spacing(2),
}))

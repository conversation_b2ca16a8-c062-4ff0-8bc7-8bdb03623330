import { isEmpty } from 'lodash'
import { DateTime } from 'luxon'
import { match, P } from 'ts-pattern'
import type { Except } from 'type-fest'
import { z } from 'zod/v4'

import {
  GeofenceReportSchema,
  getGeofenceGroupReportSchema,
  numOrNumberTypeKnownPromptSchemaShape,
  PROMPT_KNOWN_DATE_RANGE_FIELD_TYPE,
  PROMPT_KNOWN_DRIVER_FIELD_TYPE,
  PROMPT_KNOWN_VEHICLE_FIELD_TYPE,
  singleSelectTypeKnownPromptSchemaShape,
  stringTypeKnownPromptSchemaShape,
  timeTypeKnownPromptSchemaShape,
  type PromptKnownFieldType,
  type PromptKnownGeofenceGroupTypeFieldName,
  type PromptKnownNumOrNumberTypeFieldName,
  type PromptKnownSingleSelectTypeFieldName,
  type PromptKnownStringTypeFieldName,
  type PromptKnownTimeTypeFieldName,
} from 'api/reports/types'
import { emailSchema, reportFileFormatExtensionSchema } from 'api/types'
import {
  driversOrGroupsNoAllSchema,
  driversOrGroupsWithAllSchema,
} from 'src/modules/components/unconnected/MultipleSelect/shared'
import {
  createVehicleMultipleSchema,
  createVehicleOptionsSchema,
  createVehiclesOrGroupsNoAllSchema,
  createVehiclesOrGroupsSchema,
} from 'src/modules/components/unconnected/MultipleSelect/VehicleAndGroupAndTypeMultipleWithAllSelect/schema'
import { messages } from 'src/shared/forms/messages'
import { ctIntl } from 'src/util-components/ctIntl'
import {
  createNumberAsStringSchema,
  createZodDateTimeRangeSchema_edgesRequired,
  createZodObjPathGetter,
  numberAsStringBasicSchema,
} from 'src/util-functions/zod-utils'

import type { PromptsType } from './types'
import {
  DataToReceiveCustomPeriodOptions,
  DataToReceiveDurationOptions,
  handlePromptDateRangeOrDate,
  handlePromptDriver,
  handlePromptRegistration,
  ReportFrequencyCustomOptions,
  ReportFrequencyOptions,
} from './util'
import { getGeofenceFieldNoneValue } from './widgets/Geofence/GeofenceField'
import { getGeofenceGroupFieldNoneValue } from './widgets/Geofence/GeofenceGroupField'

/*****************************************Single field Schema*******************************************/

const dateTimeZodSchema = z.custom<DateTime>((value): boolean =>
  DateTime.isDateTime(value),
)

/*****************************************Form Schema*******************************************/

const commonSchema = z.object({
  sendDate: dateTimeZodSchema,
  fileFormat: reportFileFormatExtensionSchema,
  password: z.string().trim().optional(),
  isPasswordRequired: z.boolean(),
})

// TODO: - Verify if this schema is the exact same on both recurring and one time reports
export const generateReportRegistrationSchema = (prompts: PromptsType) =>
  match(handlePromptRegistration(prompts))
    .with({ widgetType: 'vehicleSingle' }, () => createVehiclesOrGroupsSchema())
    .with({ widgetType: 'vehicleNoGroupNoAll' }, () => createVehicleOptionsSchema())
    .with({ widgetType: 'vehicleNoAll' }, () => createVehiclesOrGroupsNoAllSchema())
    .with({ widgetType: 'vehicleMultiple' }, () => createVehicleMultipleSchema())
    .with(null, () => z.null())
    .exhaustive()

export const generateDateRangeSchema = (
  promptsToUse: ReturnType<typeof handlePromptDateRangeOrDate>,
) =>
  promptsToUse?.widgetType === 'dateRange'
    ? createZodDateTimeRangeSchema_edgesRequired({}).refine((val) => val[0] <= val[1], {
        message: ctIntl.formatMessage({ id: 'Start Date must be lower than End Date' }),
      })
    : z.null()

export const generateDateTimeRangeSchema = (
  promptsToUse: ReturnType<typeof handlePromptDateRangeOrDate>,
) =>
  promptsToUse?.widgetType === 'dateTimeRange'
    ? createZodDateTimeRangeSchema_edgesRequired({}).refine((val) => val[0] <= val[1], {
        message: ctIntl.formatMessage({ id: 'Start Date must be lower than End Date' }),
      })
    : z.null()

export const generateStartDateSchema = (
  promptsToUse: ReturnType<typeof handlePromptDateRangeOrDate>,
) => (promptsToUse?.widgetType === 'startDate' ? dateTimeZodSchema : z.null())

export const generateStartDateTimeSchema = (
  promptsToUse: ReturnType<typeof handlePromptDateRangeOrDate>,
) => (promptsToUse?.widgetType === 'startDateTime' ? dateTimeZodSchema : z.null())

export const generateEndDateSchema = (
  promptsToUse: ReturnType<typeof handlePromptDateRangeOrDate>,
) => (promptsToUse?.widgetType === 'endDate' ? dateTimeZodSchema : z.null())

export const generateEndDateTimeSchema = (
  promptsToUse: ReturnType<typeof handlePromptDateRangeOrDate>,
) => (promptsToUse?.widgetType === 'endDateTime' ? dateTimeZodSchema : z.null())

export type OneTimeExportFormValidValues = z.infer<
  ReturnType<typeof generateOnceExportFormSchema>
>

export type OneTimeExportFormPossibleValues = Except<
  OneTimeExportFormValidValues,
  'dateRangeOrStartOrEnd' | 'prompts' | 'emails'
> & {
  emails: OneTimeExportFormValidValues['emails'] | null
  dateRangeOrStartOrEnd: Except<
    OneTimeExportFormValidValues['dateRangeOrStartOrEnd'],
    'dateRange' | 'dateTimeRange'
  > & {
    dateRange:
      | OneTimeExportFormValidValues['dateRangeOrStartOrEnd']['dateRange']
      | [DateTime | null, DateTime | null]
    dateTimeRange:
      | OneTimeExportFormValidValues['dateRangeOrStartOrEnd']['dateTimeRange']
      | [DateTime | null, DateTime | null]
  }
} & {
  prompts: Partial<
    Except<
      OneTimeExportFormValidValues['prompts'],
      | 'geofence'
      | 'dayofweek'
      | 'registration'
      | 'driver'
      | PromptKnownGeofenceGroupTypeFieldName
    >
  > & {
    dayofweek: OneTimeExportFormValidValues['prompts']['dayofweek']
    registration: OneTimeExportFormValidValues['prompts']['registration']
    driver: OneTimeExportFormValidValues['prompts']['driver']
    geofence: OneTimeExportFormValidValues['prompts']['geofence'] | null
    geofence_group: OneTimeExportFormValidValues['prompts']['geofence_group'] | null
    geofence_group2: OneTimeExportFormValidValues['prompts']['geofence_group2'] | null
    geofence_group3: OneTimeExportFormValidValues['prompts']['geofence_group3'] | null
    geofence_group4: OneTimeExportFormValidValues['prompts']['geofence_group4'] | null
    group_geofence: OneTimeExportFormValidValues['prompts']['group_geofence'] | null
    geo_1: OneTimeExportFormValidValues['prompts']['geo_1'] | null
    geo_2: OneTimeExportFormValidValues['prompts']['geo_2'] | null
    geofence_id: OneTimeExportFormValidValues['prompts']['geofence_id'] | null
    start_geofence: OneTimeExportFormValidValues['prompts']['start_geofence'] | null
    end_geofence: OneTimeExportFormValidValues['prompts']['end_geofence'] | null
  }
}

const dateRangeOrStartDateSchema = (props: { prompts: PromptsType }) => {
  const promptsToUse = handlePromptDateRangeOrDate(props)

  return z.object({
    dateRange: generateDateRangeSchema(promptsToUse),
    dateTimeRange: generateDateTimeRangeSchema(promptsToUse),
    startDate: generateStartDateSchema(promptsToUse),
    startDateTime: generateStartDateTimeSchema(promptsToUse),
    endDate: generateEndDateSchema(promptsToUse),
    endDateTime: generateEndDateTimeSchema(promptsToUse),
  })
}

export const generateOnceExportFormSchema = ({
  prompts,
  privacyHideLocationsFromDay,
}: {
  prompts: PromptsType
  privacyHideLocationsFromDay: number
}) =>
  commonSchema
    .extend({
      sendReportToEmail: z.boolean(),
      emails: z.array(emailSchema).optional(),

      // date range or start date
      dateRangeOrStartOrEnd: dateRangeOrStartDateSchema({ prompts }),

      // prompts schema
      prompts: generateFormPromptsSchema(prompts).extend({
        registration: generateReportRegistrationSchema(prompts),
        driver: match(handlePromptDriver(prompts))
          .with({ widgetType: 'driverSingle' }, () => driversOrGroupsWithAllSchema)
          .with({ widgetType: 'driverNoAll' }, () => driversOrGroupsNoAllSchema)
          .with(null, () => z.null())
          .exhaustive(),
      }),
    })
    .superRefine((data, ctx) => {
      const { createPath } = createZodObjPathGetter(data)
      if (data.sendReportToEmail && isEmpty(data.emails)) {
        ctx.addIssue({
          code: 'custom',
          path: createPath(['emails']),
          message: ctIntl.formatMessage({
            id: 'reports.exportDrawer.emailsValidation.required',
          }),
        })
      }
      const sendDate = data.sendReportToEmail ? data.sendDate : DateTime.now()

      const endOfSendDate = sendDate
        .endOf('day')
        .minus({ days: Math.abs(privacyHideLocationsFromDay) })

      if (
        data.dateRangeOrStartOrEnd.dateRange !== null &&
        data.dateRangeOrStartOrEnd.dateRange[1] > endOfSendDate
      ) {
        ctx.addIssue({
          code: 'custom',
          path: createPath(['dateRangeOrStartOrEnd', 'dateRange']),
          message: ctIntl.formatMessage({
            id: data.sendReportToEmail
              ? 'reports.validation.dateRange.beforeSendDate'
              : 'reports.validation.dateRange.beforeCurrentDate',
          }),
        })
      }

      if (
        data.dateRangeOrStartOrEnd.dateTimeRange !== null &&
        data.dateRangeOrStartOrEnd.dateTimeRange[1] > sendDate &&
        sendDate.startOf('day') > DateTime.local().startOf('day')
      ) {
        ctx.addIssue({
          code: 'custom',
          path: createPath(['dateRangeOrStartOrEnd', 'dateTimeRange']),
          message: ctIntl.formatMessage({
            id: data.sendReportToEmail
              ? 'reports.validation.dateRange.beforeSendDate'
              : 'reports.validation.dateRange.beforeCurrentDate',
          }),
        })
      }

      if (
        data.dateRangeOrStartOrEnd.startDate !== null &&
        data.dateRangeOrStartOrEnd.startDate > endOfSendDate
      ) {
        ctx.addIssue({
          code: 'custom',
          path: createPath(['dateRangeOrStartOrEnd', 'startDate']),
          message: ctIntl.formatMessage({
            id: data.sendReportToEmail
              ? 'reports.validation.startDate.beforeSendDate'
              : 'reports.validation.startDate.beforeCurrentDate',
          }),
        })
      }

      if (
        data.dateRangeOrStartOrEnd.startDateTime !== null &&
        data.dateRangeOrStartOrEnd.startDateTime > sendDate
      ) {
        ctx.addIssue({
          code: 'custom',
          path: createPath(['dateRangeOrStartOrEnd', 'startDateTime']),
          message: ctIntl.formatMessage({
            id: data.sendReportToEmail
              ? 'reports.validation.startDate.beforeSendDate'
              : 'reports.validation.startDate.beforeCurrentDate',
          }),
        })
      }
      if (data.isPasswordRequired && !data.password) {
        ctx.addIssue({
          code: 'custom',
          message: ctIntl.formatMessage({ id: 'Password is required' }),
          path: ['password'],
        })
      }
    })

export type ScheduleExportFormValidSchema = z.infer<
  ReturnType<typeof generateScheduleExportFormSchema>
>

export type RecurringExportFormPossible = Except<
  ScheduleExportFormValidSchema,
  'prompts' | 'emails' | 'sendDate'
> & {
  emails: ScheduleExportFormValidSchema['emails'] | null
  sendDate: ScheduleExportFormValidSchema['sendDate'] | null
  prompts: Partial<
    Except<
      ScheduleExportFormValidSchema['prompts'],
      | 'geofence'
      | 'dayofweek'
      | 'registration'
      | 'driver'
      | PromptKnownGeofenceGroupTypeFieldName
    >
  > & {
    dayofweek: ScheduleExportFormValidSchema['prompts']['dayofweek']
    registration: ScheduleExportFormValidSchema['prompts']['registration']
    driver: ScheduleExportFormValidSchema['prompts']['driver']
    geofence: ScheduleExportFormValidSchema['prompts']['geofence'] | null
    geofence_group: OneTimeExportFormValidValues['prompts']['geofence_group'] | null
    geofence_group2: OneTimeExportFormValidValues['prompts']['geofence_group2'] | null
    geofence_group3: OneTimeExportFormValidValues['prompts']['geofence_group3'] | null
    geofence_group4: OneTimeExportFormValidValues['prompts']['geofence_group4'] | null
    group_geofence: OneTimeExportFormValidValues['prompts']['group_geofence'] | null
    geo_1: OneTimeExportFormValidValues['prompts']['geo_1'] | null
    geo_2: OneTimeExportFormValidValues['prompts']['geo_2'] | null
    geofence_id: OneTimeExportFormValidValues['prompts']['geofence_id'] | null
    start_geofence: OneTimeExportFormValidValues['prompts']['start_geofence'] | null
    end_geofence: OneTimeExportFormValidValues['prompts']['end_geofence'] | null
  }
  repeatInterval: {
    reportFrequency: RepeatIntervalSchema['reportFrequency']
    intervalDaysOfWeek: RepeatIntervalSchema['intervalDaysOfWeek']
    intervalDayOfMonth: 'first' | 'last' | `custom:${number}`
    customInterval: RepeatIntervalSchema['customInterval']
  }
}

const createRepeatIntervalSchema = () =>
  z
    .object({
      reportFrequency: z.union([
        z.literal(ReportFrequencyOptions.DAILY),
        z.literal(ReportFrequencyOptions.WEEKLY),
        z.literal(ReportFrequencyOptions.MONTHLY),
        z.literal(ReportFrequencyOptions.CUSTOM),
      ]),
      intervalDaysOfWeek: z.array(numberAsStringBasicSchema),
      intervalDayOfMonth: z.string(),
      customInterval: z.tuple([
        createNumberAsStringSchema({
          customParams: { message: ctIntl.formatMessage({ id: messages.required }) },
        }),
        z.union([
          z.literal(ReportFrequencyCustomOptions.DAYS),
          z.literal(ReportFrequencyCustomOptions.WEEKS),
          z.literal(ReportFrequencyCustomOptions.MONTHS),
        ]),
      ]),
    })
    .superRefine((obj, ctx) => {
      const { createPath } = createZodObjPathGetter(obj)

      // days of week is required when repeat interval is Weekly
      if (
        obj.reportFrequency === ReportFrequencyOptions.WEEKLY &&
        isEmpty(obj.intervalDaysOfWeek)
      ) {
        ctx.addIssue({
          code: 'custom',
          message: ctIntl.formatMessage({ id: 'You need to select at least one day' }),
          path: createPath(['intervalDaysOfWeek']),
        })
      }

      // day input is required when select Monthly -> Selected day of the month
      if (
        obj.reportFrequency === ReportFrequencyOptions.MONTHLY &&
        obj.intervalDayOfMonth.includes('custom') &&
        !obj.intervalDayOfMonth?.split(':')[1]
      ) {
        ctx.addIssue({
          code: 'custom',
          message: ctIntl.formatMessage({ id: messages.required }),
          path: createPath(['intervalDayOfMonth']),
        })
      }

      // custom interval input is required when select Custom interval
      if (
        obj.reportFrequency === ReportFrequencyOptions.CUSTOM &&
        !Number(obj.customInterval[0])
      ) {
        ctx.addIssue({
          code: 'custom',
          message: ctIntl.formatMessage({ id: messages.required }),
          path: createPath(['customInterval']),
        })
      }
    })

type RepeatIntervalSchema = z.infer<ReturnType<typeof createRepeatIntervalSchema>>

const createDataDurationSchema = () =>
  z
    .object({
      dataToReceiveDuration: z.enum([
        DataToReceiveDurationOptions.PREVIOUS_DAY,
        DataToReceiveDurationOptions.PREVIOUS_7_DAYS,
        DataToReceiveDurationOptions.PREVIOUS_30_DAYS,
        DataToReceiveDurationOptions.PREVIOUS_MONTH,
        DataToReceiveDurationOptions.BEGINNING_OF_THE_MONTH_TILL_DATE,
        DataToReceiveDurationOptions.CUSTOM_DURATION,
        DataToReceiveDurationOptions.ONE_MONTH_AGO_TO_DATE,
      ]),
      dataToReceiveCustomPeriod: z.tuple([
        createNumberAsStringSchema({
          customParams: { message: ctIntl.formatMessage({ id: messages.required }) },
        }),
        z.enum([
          DataToReceiveCustomPeriodOptions.HOURS,
          DataToReceiveCustomPeriodOptions.DAYS,
          DataToReceiveCustomPeriodOptions.WEEKS,
          DataToReceiveCustomPeriodOptions.MONTHS,
        ]),
      ]),
    })
    .superRefine((obj, ctx) => {
      const { createPath } = createZodObjPathGetter(obj)

      // data custom duration input is required when select Custom duration
      if (
        obj.dataToReceiveDuration === DataToReceiveDurationOptions.CUSTOM_DURATION &&
        Number(obj.dataToReceiveCustomPeriod[0]) < 1
      ) {
        ctx.addIssue({
          code: 'custom',
          message: ctIntl.formatMessage({ id: messages.required }),
          path: createPath(['dataToReceiveCustomPeriod']),
        })
      }
    })

export const generateScheduleExportFormSchema = ({
  prompts,
}: {
  prompts: PromptsType
}) =>
  commonSchema
    .extend({
      emails: z
        .array(emailSchema)
        .min(1, { message: 'reports.exportDrawer.emailsValidation.required' }),

      // date range or start date
      dateRangeOrStartOrEnd: dateRangeOrStartDateSchema({ prompts }),

      // interval
      repeatInterval: createRepeatIntervalSchema(),

      // duration for receive data
      dataDuration: createDataDurationSchema(),

      // prompts schema
      prompts: generateFormPromptsSchema(prompts).extend({
        registration: generateReportRegistrationSchema(prompts),
        driver: match(handlePromptDriver(prompts))
          .with({ widgetType: 'driverSingle' }, () => driversOrGroupsWithAllSchema)
          .with({ widgetType: 'driverNoAll' }, () => driversOrGroupsNoAllSchema)
          .with(null, () => z.null())
          .exhaustive(),
      }),
    })
    .superRefine((data, ctx) => {
      if (data.isPasswordRequired && !data.password) {
        ctx.addIssue({
          code: 'custom',
          message: ctIntl.formatMessage({ id: 'Password is required' }),
          path: ['password'],
        })
      }
    })

/*****************************************Initial values for forms*******************************************/

export const numberTypePromptsInitialValues = {
  in_interval: '',
  min_visits: '',
  speed_limiter: '',
  speed_limit: '',
  pi_price_km_pay: '',
  pi_price_km_recieve: '',
  time_open: '',
  time_seconds: '',
  temp_seg: '',
  swathe: '',
  trip_limit: '',
  year: '',
  interval: '',
  min_duration: '',
  in_event_limit: '',
  in_top_vehicle_limit: '',
  speed: '',
  cost: '',
  Idlelimit: '',
  idle_minutes_min: '',
} satisfies Record<PromptKnownNumOrNumberTypeFieldName, ''>

export const stringTypePromptsInitialValues = {
  // DESC
  AvgFuel: '',
  FuelCost: '',
  Title1: '',
  Title2: '',
  Title3: '',
  km_cost: '',
  reg: '',
  vmin: '',
  accel: '',
  braking: '',
  corner: '',
  grouped: '',
  pto_distance: '',
  folgas: '',

  // USERNAME
  username: '',
  user_name: '',

  // USER_ID
  client_user_id: '',
  user_id: '',
  pi_company_id: '',

  // BRANCH
  branch_id: '',
  rest_days: '',

  minutes: '',
} satisfies Record<PromptKnownStringTypeFieldName, ''>

export const timeTypePromptsInitialValues = {
  working_hours_end: null,
  working_hours_start: null,
  end_time: null,
  start_time: null,
  hours_start: null,
  end_time_afternoon: null,
  end_time_morning: null,
  start_time_afternoon: null,
  start_time_morning: null,
  Time_End: null,
  Time_Start: null,
  work_end: null,
  work_start: null,
  abnormal_end: null,
  abnormal_start: null,
  start_work_hours: null,
  end_work_hours: null,
} satisfies Record<PromptKnownTimeTypeFieldName, null>

export const dayOfWeekTypePromptsInitialValues = {
  dayofweek: null,
}

export const singleSelectTypePromptsInitialValues = {
  updown: null,
  duration: null,
  profile_id: null,
} satisfies Record<PromptKnownSingleSelectTypeFieldName, null>

export const getGeofenceTypePromptsInitialValues = () => ({
  geofence: getGeofenceFieldNoneValue(),
  geofence_group: getGeofenceGroupFieldNoneValue(),
  geofence_group2: getGeofenceGroupFieldNoneValue(),
  geofence_group3: getGeofenceGroupFieldNoneValue(),
  geofence_group4: getGeofenceGroupFieldNoneValue(),
  group_geofence: getGeofenceGroupFieldNoneValue(),
  geo_1: getGeofenceFieldNoneValue(),
  geo_2: getGeofenceFieldNoneValue(),
  geofence_id: getGeofenceFieldNoneValue(),
  start_geofence: getGeofenceFieldNoneValue(),
  end_geofence: getGeofenceFieldNoneValue(),
})

/*****************************************Dynamic Generate Prompt Schema*******************************************/

export const insertPromptWidgetType = ({ prompts }: { prompts: PromptsType }) => {
  if (!prompts) return []

  const filteredPrompts = prompts.filter(
    ({ type }) =>
      !(
        [
          ...PROMPT_KNOWN_VEHICLE_FIELD_TYPE,
          ...PROMPT_KNOWN_DRIVER_FIELD_TYPE,
          ...PROMPT_KNOWN_DATE_RANGE_FIELD_TYPE,
        ] as Array<PromptKnownFieldType>
      ).includes(type.trim() as PromptKnownFieldType),
  )

  // NOTE: The reason we dynamically setup the schema is the identifier is not unpredictable, most schemas
  // are decided by the field type. But we still need the identifier as field name for form submitting,
  // because some may need the help of identifier
  return filteredPrompts.map((prompt) => ({
    ...prompt,
    widgetType: match(prompt)
      .returnType<
        | 'geofence'
        | 'geofenceGroup'
        | 'number'
        | 'string'
        | 'time'
        | 'singleSelect'
        | 'daysOfWeek'
      >()
      .with({ type: 'GEOFENCE' }, () => 'geofence')
      .with({ type: 'GEOFENCEGROUP' }, () => 'geofenceGroup')
      .with({ type: P.union('NUM', 'NUMBER', 'INTERVAL') }, () => 'number')
      .with(
        {
          type: P.union('DESC', 'BRANCH', 'USER_NAME', 'USER_ID'),
          identifier: P.not(P.union('working_hours_end', 'working_hours_start')),
        },
        () => 'string',
      )
      // NOTE: working_hours_end, working_hours_start can be DESC type, need to handle them separately
      .with(
        {
          type: 'DESC',
          identifier: P.union('working_hours_end', 'working_hours_start'),
        },
        () => 'time',
      )
      .with({ type: P.union('TIME', 'TIME-E', 'TIME-S') }, () => 'time')
      .with(
        { type: P.union('LIST(UP/DOWN/NONE)', 'TIMEFINE', 'PROFILE') },
        () => 'singleSelect',
      )

      .with({ type: 'DOW' }, () => 'daysOfWeek')
      // .with('POI', () => {})
      // NOTE: for all the other unknown types widget, treat it as string
      .otherwise(() => 'string'),
  }))
}

// NOTE: we list all the possible fields with explicit schema here no matter what prompts are
// because react-hook-form cannot recognise the dynamic schema
const generateFormPromptsSchema = (prompts: PromptsType) =>
  z
    .object({
      dayofweek: z.array(z.string()).min(1, { message: messages.required }).nullable(),
    })
    .merge(getGeofenceGroupReportSchema())
    .merge(GeofenceReportSchema)
    .extend(generateStringNumberFieldsSchema(prompts))
    .extend(generateSingleSelectFieldsSchema(prompts))
    .extend(generateTimeFieldsSchema(prompts))

const generateSingleSelectFieldsSchema = (prompts: PromptsType) => {
  const acc: Record<
    PromptKnownSingleSelectTypeFieldName,
    z.ZodNullable<z.ZodString> | z.ZodString
  > = {
    ...singleSelectTypeKnownPromptSchemaShape,
  }

  for (const identifier of Object.keys(singleSelectTypeKnownPromptSchemaShape)) {
    if (prompts?.some((p) => p.identifier === identifier)) {
      acc[identifier as PromptKnownSingleSelectTypeFieldName] = z
        .string()
        .min(1, ctIntl.formatMessage({ id: messages.required }))
    }
  }

  return acc
}

const generateStringNumberFieldsSchema = (prompts: PromptsType) => {
  const acc: Record<
    PromptKnownNumOrNumberTypeFieldName | PromptKnownStringTypeFieldName,
    z.ZodOptional<z.ZodString> | z.ZodString
  > = {
    ...numOrNumberTypeKnownPromptSchemaShape,
    ...stringTypeKnownPromptSchemaShape,
  }

  for (const identifier of Object.keys({
    ...numOrNumberTypeKnownPromptSchemaShape,
    ...stringTypeKnownPromptSchemaShape,
  })) {
    if (prompts?.some((p) => p.identifier === identifier)) {
      acc[
        identifier as
          | PromptKnownNumOrNumberTypeFieldName
          | PromptKnownStringTypeFieldName
      ] = z.string().min(1, ctIntl.formatMessage({ id: messages.required }))
    }
  }

  return acc
}

const generateTimeFieldsSchema = (prompts: PromptsType) => {
  const acc: Record<
    PromptKnownTimeTypeFieldName,
    z.ZodNullable<z.ZodType<DateTime, DateTime>> | z.ZodType<DateTime, DateTime>
  > = { ...timeTypeKnownPromptSchemaShape }

  for (const identifier of Object.keys(timeTypeKnownPromptSchemaShape)) {
    if (prompts?.some((p) => p.identifier.trim() === identifier)) {
      acc[identifier as PromptKnownTimeTypeFieldName] = z.custom<DateTime>(
        (value): boolean => DateTime.isDateTime(value),
        ctIntl.formatMessage({ id: messages.required }),
      )
    }
  }

  return acc
}

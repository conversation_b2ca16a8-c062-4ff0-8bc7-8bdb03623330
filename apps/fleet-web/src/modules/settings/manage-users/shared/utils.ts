import { z } from 'zod/v4'

import { getPhoneInfoSchema } from 'src/shared/react-hook-form/PhoneNumberInput'
import { ctIntl } from 'src/util-components/ctIntl'
import { generateEmailSchema, generateStringSchema } from 'src/util-functions/zod-utils'

import { createSubUserUsernameSchema } from './validation-schema'

export const getFleetUserSchema = () =>
  z.object({
    username: createSubUserUsernameSchema(ctIntl),
    cellPhone: getPhoneInfoSchema({ isRequired: true }),
    email: generateEmailSchema({ isRequired: true }),
    departmentId: z.number().nullable(),
    // Right now, we are not using manageRolesSetting to conditionally set the schema but it can be useful in the future (e.g if clientUserRoleId becomes required only when manageRolesSetting is true)
    clientUserRoleId: z.string().nullable(),
  })

export const getFleetUserWithCostsSchema = () =>
  z
    .object({
      name: generateStringSchema({
        isRequired: true,
        maxLength: 50,
      }),
      languageId: z.string().min(1),
      parentUserId: z.string().nullable().optional(),
      user_role_id: z.array(z.string()).optional(),
    })
    .merge(getFleetUserSchema())

export const getConditionalFleetUserWithCostsSchema = (isRequired: boolean) =>
  getFleetUserWithCostsSchema().superRefine((data, ctx) => {
    if (isRequired) {
      if (!data.name) {
        ctx.addIssue({
          path: ['name'],
          message: 'Name is required',
          code: 'custom',
        })
      }
      if (!data.languageId) {
        ctx.addIssue({
          path: ['languageId'],
          message: 'Language ID is required',
          code: 'custom',
        })
      }
      if (!data.user_role_id || data.user_role_id.length === 0) {
        ctx.addIssue({
          path: ['user_role_id'],
          message: 'User role ID is required',
          code: 'custom',
        })
      }
    }
  })

export type FleetUserSchema = z.infer<ReturnType<typeof getFleetUserSchema>>

export type FleetUserWithCostsSchema = z.infer<
  ReturnType<typeof getFleetUserWithCostsSchema>
>

export type UserProfileSchema = FleetUserWithCostsSchema

import type { ctIntl } from 'src/util-components/ctIntl'
import { generateStringSchema } from 'src/util-functions/zod-utils'

const invalidUsernameChars = ['`', '(', ')', ':', ';', '"', "'", '*']
const invalidUsernameCharsRegex = new RegExp(`[${invalidUsernameChars.join('')}\\s]`)

export const createSubUserUsernameSchema = (
  intl: Pick<typeof ctIntl, 'formatMessage'>,
) =>
  generateStringSchema(
    {
      isRequired: true,
      maxLength: 50,
    },
    intl,
  ).refine((val) => !invalidUsernameCharsRegex.test(val), {
    error: () =>
      intl.formatMessage(
        { id: 'formValidation.subUser.name.errorMsg' },
        { values: { invalidChars: invalidUsernameChars.join(' ') } },
      ),
  })

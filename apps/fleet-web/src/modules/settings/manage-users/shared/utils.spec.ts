import { describe, it } from 'vitest'
import { vExpect } from 'src/vitest/utils'

import { createSubUserUsernameSchema } from './validation-schema'

const mockCtIntl = {
  formatMessage: () => '',
}

describe('createSubUserUsernameSchema', () => {
  it('should allow valid usernames', () => {
    const schema = createSubUserUsernameSchema(mockCtIntl)
    vExpect(schema.safeParse('validusername').success).toEqual(true)
    vExpect(schema.safeParse('validusername12e').success).toEqual(true)
    vExpect(schema.safeParse('valid_username').success).toEqual(true)
    vExpect(schema.safeParse('valid.username').success).toEqual(true)
    vExpect(schema.safeParse('valid-username').success).toEqual(true)
    vExpect(schema.safeParse('valid#username').success).toEqual(true)
    vExpect(schema.safeParse('valid_username123').success).toEqual(true)
    vExpect(schema.safeParse('valid@username123').success).toEqual(true)
    vExpect(schema.safeParse('valid[]@_.-&!$%^/|,&username123').success).toEqual(true)
  })

  it('should reject usernames with invalid characters', () => {
    const schema = createSubUserUsernameSchema(mockCtIntl)
    vExpect(schema.safeParse('invalid username').success).toEqual(false)
    vExpect(schema.safeParse('invalid   username').success).toEqual(false)
    vExpect(schema.safeParse('invalid`username').success).toEqual(false)
    vExpect(schema.safeParse('invalid()username').success).toEqual(false)
    vExpect(schema.safeParse('invalid:username').success).toEqual(false)
    vExpect(schema.safeParse('invalid;username').success).toEqual(false)
    vExpect(schema.safeParse('invalid"username').success).toEqual(false)
    vExpect(schema.safeParse("invalid'username").success).toEqual(false)
    vExpect(schema.safeParse('invalid*username').success).toEqual(false)
  })
})

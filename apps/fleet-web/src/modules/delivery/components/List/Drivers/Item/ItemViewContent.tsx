import { useContext, useMemo } from 'react'
import { DateTime } from 'luxon'
import styled from 'styled-components'
import { match, P } from 'ts-pattern'

import { DeliveryContext } from 'src/modules/delivery'
import Box from 'src/modules/delivery/components/Box'
import CircularAvatar from 'src/modules/delivery/components/CircularAvatar'
import Popover from 'src/modules/delivery/components/popover'
import { CustomTooltip } from 'src/modules/delivery/components/popover/styles'
//components
import ProgressBar from 'src/modules/delivery/components/ProgressBar'
//hooks
import useGetSubUsersById from 'src/modules/delivery/hooks/sub-user/useGetSubUsersById'
//styled
import {
  PanelListItemDetailsContainer,
  PanelListItemDetailsHeader,
  PanelListItemScheduleContainer,
  PanelListItemStatusContainer,
} from 'src/modules/delivery/styled/PanelStyles'
import checkSubUserAccessible, {
  canThisUserSeeSubuser,
} from 'src/modules/delivery/utils/checkSubUserAccessible'
//constants
import {
  DELIVERY_CIRCULAR_INITIAL_HEXCODE,
  DELIVERY_COLOR,
} from 'src/modules/delivery/utils/constants'
import { generateHexCode } from 'src/modules/delivery/utils/helpers'
import { ctIntl } from 'src/util-components/ctIntl'
//utils
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

// svg
import lockSVG from 'assets/delivery/svg/lock.svg'
import userSVG from 'assets/svg/Delivery/driver-user-icon.svg'

import { themeType } from '../../../Theme'
//types
import type { DeliveryDriverDetails } from './ItemView'

const STATUS_COLOR = {
  OFFLINE: DELIVERY_COLOR.GREY6,
  NOT_ACTIVE: DELIVERY_COLOR.ORANGE,
  ON_BREAK: DELIVERY_COLOR.RED,
  ONLINE: DELIVERY_COLOR.GREEN,
} as const

function checkIsNotActive(ts: string, mins: number) {
  return Math.abs(DateTime.fromJSDate(new Date(ts)).diffNow('minutes').minutes) >= mins
}

type Props = {
  driver: DeliveryDriverDetails
  isPopover?: boolean
  customIcon?: string
  isActive?: boolean
  jobProgressPercentage?: number
  jobProgressLabel?: string
}

const ItemViewContent = ({
  driver,
  isPopover,
  customIcon,
  isActive,
  jobProgressPercentage,
  jobProgressLabel,
}: Props) => {
  const { subUser } = useGetSubUsersById({ subUserId: driver.subuserId })

  const { deliverySettings } = useContext(DeliveryContext)

  const { driverMaxStopsBusy } = deliverySettings

  const { driverStatusColor, driverStatusDescription } = useMemo(
    () =>
      match(driver)
        .with(
          { offlineModeSinceTs: P.nonNullable },
          { isLoggedIn: false, offlineModeSinceTs: P.nullish },
          () => ({
            driverStatusColor: STATUS_COLOR.OFFLINE,
            driverStatusDescription: ctIntl.formatMessage({ id: 'Offline' }),
          }),
        )
        .with(
          {
            onBreakSinceTs: P.nonNullable,
          },
          {
            isLoggedIn: true,
            stopLeftNumber: P.when((value) => value > Number(driverMaxStopsBusy)),
          },
          () => ({
            driverStatusColor: STATUS_COLOR.ON_BREAK,
            driverStatusDescription: ctIntl.formatMessage({ id: 'On break' }),
          }),
        )
        .with(
          {
            isLoggedIn: true,
            lastOnlineTs: P.when(
              (lastOnlineTs) =>
                lastOnlineTs &&
                checkIsNotActive(
                  lastOnlineTs,
                  Number(deliverySettings.driverTimeoutOffline),
                ),
            ),
          },
          () => ({
            driverStatusColor: STATUS_COLOR.NOT_ACTIVE,
            driverStatusDescription: ctIntl.formatMessage({ id: 'Not active' }),
          }),
        )
        .with(
          {
            isLoggedIn: true,
          },
          () => ({
            driverStatusColor: STATUS_COLOR.ONLINE,
            driverStatusDescription: ctIntl.formatMessage({ id: 'Online' }),
          }),
        )
        .otherwise(() => ({
          driverStatusColor: STATUS_COLOR.OFFLINE,
          driverStatusDescription: ctIntl.formatMessage({ id: 'Offline' }),
        })),
    [deliverySettings.driverTimeoutOffline, driver, driverMaxStopsBusy],
  )

  return (
    <Box
      padding="2px 5px 1px"
      display="flex"
      width="100%"
      alignItems={isPopover ? 'flex-start' : 'center'}
    >
      <PanelListItemStatusContainer>
        <DriverIcon
          {...makeSanitizedInnerHtmlProp({
            dirtyHtml: !driver.fleetDriverId ? lockSVG : customIcon || userSVG,
          })}
          isActive={isActive}
        />
        {!customIcon && (
          <StatusIcon
            title={ctIntl.formatMessage({
              id: driverStatusDescription,
            })}
            bgcolor={driverStatusColor}
          >
            {driver.driverStatus.color === 'OFFLINE' && (
              <OfflineIcon bgcolor={driverStatusColor} />
            )}
          </StatusIcon>
        )}
      </PanelListItemStatusContainer>
      <PanelListItemDetailsContainer isActive={isActive}>
        <PanelListItemDetailsHeader
          isActive={isActive}
          className="driverName"
        >
          {driver.fullName}
        </PanelListItemDetailsHeader>
        <Box
          display="flex"
          width="100%"
          justifyContent="space-between"
          color={isPopover ? themeType.light.grey22 : 'inherit'}
        >
          <Box
            width="100%"
            textOverflow="ellipsis"
            whiteSpace="nowrap"
            overflow="hidden"
          >
            {driver.registration}
          </Box>
          {isPopover && (
            <Box marginLeft="auto">
              <StatusWrapper>
                {ctIntl.formatMessage({
                  id: driver.driverStatus.description,
                })}
                {driver.isPlanning && (
                  <LockSvg
                    {...makeSanitizedInnerHtmlProp({ dirtyHtml: lockSVG })}
                    isActive={isActive}
                  />
                )}
              </StatusWrapper>
            </Box>
          )}
        </Box>
        {isPopover && (
          <Box marginTop="5px">
            <ProgressBar
              defaultFillColor={themeType.light.grey1}
              fillColor={DELIVERY_COLOR.GREEN}
              completed={jobProgressPercentage || 0}
              width="100%"
              height="4px"
            />
            <Box
              display="flex"
              justifyContent="space-between"
              width=" 100%"
              color={themeType.light.grey22}
              padding="5px 0"
            >
              <Box>
                {driver.stopsCompleted || 0}/{driver.stopsTotal || 0}
              </Box>
              <Box>{jobProgressLabel}</Box>
            </Box>
          </Box>
        )}
      </PanelListItemDetailsContainer>
      {!customIcon && !isPopover && (
        <PanelListItemScheduleContainer>
          <StatusWrapper>
            {/*// DWA-3131 Disable Chat*/}
            {/*{unreadMessagesCount > 0 && (*/}
            {/*  <Badge*/}
            {/*    text={unreadMessagesCount}*/}
            {/*    isActive={isActive}*/}
            {/*  />*/}
            {/*)}*/}
            {driver.stopsCompleted || 0}/{driver.stopsTotal || 0}
          </StatusWrapper>
          <StatusWrapper>
            {/* Limiting the access of sub-user fields and will be controlled in the DB */}
            {/* [owned_plus_admin]: gives access to sub-user field and feature */}
            {subUser && checkSubUserAccessible() && canThisUserSeeSubuser() && (
              <Popover
                trigger="hover"
                position="right"
                customContent={
                  <CustomTooltip>
                    {ctIntl.formatMessage(
                      {
                        id: 'Managed by {subuserName}',
                      },
                      {
                        values: {
                          subuserName: subUser.username,
                        },
                      },
                    )}
                  </CustomTooltip>
                }
                popoverBackground="rgba(0, 0, 0, 0.7)"
                zIndex={1302}
              >
                <CircularAvatar
                  name={subUser.username}
                  background={generateHexCode(
                    subUser.id,
                    DELIVERY_CIRCULAR_INITIAL_HEXCODE,
                  )}
                />
              </Popover>
            )}
            {ctIntl.formatMessage({
              id: driverStatusDescription,
            })}
            {driver.isPlanning && (
              <LockSvg
                {...makeSanitizedInnerHtmlProp({ dirtyHtml: lockSVG })}
                isActive={isActive}
              />
            )}
            {/* Temporarily removing for https://cartrack.atlassian.net/browse/DWA-2113
    as we need the BE support for this flag
    {deliveryDriverMetricCapacity?.isCapableToHandleLoad !==
      undefined &&
      !deliveryDriverMetricCapacity?.isCapableToHandleLoad && (
        <AlertSvg
          {...makeSanitizedInnerHtmlProp({ dirtyHtml: AlertIcon })}
          isActive={isActive}
        />
      )} */}
          </StatusWrapper>
        </PanelListItemScheduleContainer>
      )}
    </Box>
  )
}

export default ItemViewContent

const StatusWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 3px;
`

const OfflineIcon = styled.div<{ bgcolor: string }>`
  height: 6px;
  width: 6px;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  background-color: ${(p) => p.bgcolor || 'transparent'};
`

const StatusIcon = styled.div<{ bgcolor: string }>`
  height: 10px;
  width: 10px;
  background-color: ${(p) => p.bgcolor || 'transparent'};
  border-radius: 50%;
  position: absolute;
  right: 0;
  bottom: 0;
  margin-right: 3px;
  border: solid 1px #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
`

const DriverIcon = styled.svg<{ isActive?: boolean }>`
  height: 25px;
  width: 25px;

  path {
    fill: ${({ isActive }) => (isActive ? '#FFFFFF' : '')};
  }

  g > circle {
    stroke: ${({ isActive }) => (isActive ? '#FFFFFF' : '')};
  }
  g > #rejectXclamationDot {
    fill: ${({ isActive }) => (isActive ? '#FFFFFF' : '')};
  }
`

const WidgetsSvg = styled.svg`
  width: 15px;
  height: 15px;
`

export const LockSvg = styled(WidgetsSvg)<{ isActive?: boolean }>`
  z-index: 501;
  path {
    fill: ${({ isActive }) => (isActive ? '#FFFFFF' : themeType.light.warning)};
  }
`
export const AlertSvg = styled(WidgetsSvg)<{ isActive?: boolean }>`
  path {
    fill: ${({ isActive }) => isActive && '#FFFFFF'};
  }
`

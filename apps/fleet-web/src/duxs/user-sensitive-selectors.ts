// ---------------------------------------------------------------------
// This file contains selectors that are imported from files that are prone to circular dependencies. By putting them outside of duxs/user, we can avoid circular dependencies.
// in multiple places.
// ---------------------------------------------------------------------

import { match } from 'ts-pattern'
import type { Settings } from 'api/types'
import { isBoolean, isEmpty, isNil } from 'lodash'
import moment from 'moment-timezone'
import { createSelector } from '@reduxjs/toolkit'
import { ctIntl } from 'src/util-components/ctIntl'
import type { AppState } from 'src/root-reducer'
import type { BCP47LanguageTag, FixMeAny } from 'src/types'
import { type PositionDescription, reportIdSchema } from 'api/types'
import { MapApiProvider, mapApiProviderSet } from 'api/user/types'
import type { Login } from 'api/user/types'
import { isBETrue } from 'api/utils'
import {
  isNilOrEmptyString,
  isNonEmptyTrimmedString,
} from 'src/util-functions/string-utils'
import { parseBooleanUserSetting, parseNumberUserSetting } from './utils'
import { z } from 'zod/v4'
import { createSelectorWithStrictMode } from 'src/redux-utils'
import * as R from 'remeda'
import {
  Array_filterMap,
  Array_uniqBy,
} from 'src/util-functions/performance-critical-utils'
import type { NonEmptyArray } from 'src/types/utils'
import type { Tagged } from 'type-fest'
import type { PermissionsAppState } from 'src/modules/app/components/routes/types'
import type { FacilityTranslationTermsBanned } from 'src/util-components/intlBannedTerms'

const guessedLocalTimeZone = moment.tz.guess()

export const getTimeZones = (state: AppState) => state.user.timeZones

export const getSettings = (state: Pick<PermissionsAppState, 'user'>) =>
  state.user.settings

/**
 * @deprecated __Use `getSettings` instead.__
 *
 * This selector casts the settings to a `Record<string, any>` type to avoid errors.
 *
 * What you __should do__ instead is to create a selector for each setting you want to use. It should be validated to make sure it is the type you expect.
 */
export const getSettings_UNSAFE = (state: PermissionsAppState) =>
  getSettings(state) as Record<string, any>

// MiFleet Language options
const mifleetLanguageOptionSchema = z.object({
  name: z.string(),
  value: z.string(),
})

const mifleetArrayOptionsSchema = z.array(mifleetLanguageOptionSchema)

export const getDeliveryAppointmentsSetting = (state: AppState) => {
  const { deliveryAppointments } = getSettings(state)
  return parseBooleanUserSetting(deliveryAppointments, {
    defaultValueWhenNotValid: false,
  })
}

export const getMifleetLanguagesOptions = createSelectorWithStrictMode(
  getSettings,
  ({ mifleetLanguagesOptions }): ReadonlyArray<{ name: string; value: string }> => {
    const parseResult = mifleetArrayOptionsSchema.safeParse(mifleetLanguagesOptions)
    if (parseResult.success) {
      return parseResult.data
    }
    return []
  },
)

export const getDefaultMapApiProviderSetting = (state: AppState): MapApiProvider => {
  const { mapApiProvider } = getSettings(state)
  const defaultValue = MapApiProvider.GOOGLE

  if (isNilOrEmptyString(mapApiProvider)) {
    return defaultValue
  }

  const asNumber = Number(mapApiProvider)
  if (Number.isNaN(asNumber)) {
    return defaultValue
  }

  return mapApiProviderSet.has(asNumber as any)
    ? (asNumber as MapApiProvider)
    : defaultValue
}

export const getDriversEditDriver = (state: AppState) => {
  const { driversEditDriver } = getSettings(state)
  return parseBooleanUserSetting(driversEditDriver, {
    defaultValueWhenNotValid: false,
  })
}

export const getListDriverPassport = (state: AppState) => {
  const { listDriverPassport } = getSettings(state)
  return parseBooleanUserSetting(listDriverPassport, {
    defaultValueWhenNotValid: false,
  })
}

export const getUserProfileSettingsEditUser = (state: AppState) => {
  const { userProfileSettingsEditUser } = getSettings(state)
  return parseBooleanUserSetting(userProfileSettingsEditUser, {
    defaultValueWhenNotValid: false,
  })
}

export const getShowRoadDescriptionTypeSetting = (state: AppState) => {
  const { showRoadDescriptionTypeSetting } = getSettings(state)
  return parseBooleanUserSetting(showRoadDescriptionTypeSetting, {
    defaultValueWhenNotValid: false,
  })
}

export const getTimezoneSettings = (state: AppState) => {
  const { timezoneSettings } = getSettings(state)
  return parseBooleanUserSetting(timezoneSettings, {
    defaultValueWhenNotValid: false,
  })
}

export const getTimezoneForceServerTimeSettings = (state: AppState) => {
  const { timezoneForceServerTime } = getSettings(state)
  return parseBooleanUserSetting(timezoneForceServerTime, {
    defaultValueWhenNotValid: false,
  })
}

export const getCarpool = (state: AppState) => {
  const { carpool } = getSettings(state)
  return parseBooleanUserSetting(carpool, {
    defaultValueWhenNotValid: false,
  })
}

export const getShowCarpoolStats = (state: AppState) => {
  const { showCarpoolStats } = getSettings(state)
  return parseBooleanUserSetting(showCarpoolStats, {
    defaultValueWhenNotValid: false,
  })
}

export const getMapCompareVehicles = (state: AppState) => {
  const { mapCompareVehicles } = getSettings(state)
  return parseBooleanUserSetting(mapCompareVehicles, {
    defaultValueWhenNotValid: false,
  })
}

export const getCoachingVehicleHoursToCheckForInactivitySetting = (
  state: AppState,
): number => {
  const { coachingVehicleHoursToCheckForInactivity } = getSettings(state)

  if (isNilOrEmptyString(coachingVehicleHoursToCheckForInactivity)) {
    return 24
  }

  const parsedValue = Number(coachingVehicleHoursToCheckForInactivity)

  return Number.isNaN(parsedValue) || parsedValue === 0 ? 24 : parsedValue
}

export const getLandmarksAddPOISetting = (state: AppState) => {
  const { landmarksAddPOI } = getSettings(state)

  return parseBooleanUserSetting(landmarksAddPOI, {
    defaultValueWhenNotValid: false,
  })
}

export const getGeofencesAddGeofenceSetting = (state: AppState) => {
  const { geofencesAddGeofence } = getSettings(state)

  return parseBooleanUserSetting(geofencesAddGeofence, {
    defaultValueWhenNotValid: false,
  })
}

export const getVehiclesViewStatus = (state: AppState) => {
  const { vehiclesViewStatus } = getSettings(state)

  return parseBooleanUserSetting(vehiclesViewStatus, {
    defaultValueWhenNotValid: false,
  })
}

export const getHideVehicleRecentActivity = (state: AppState) => {
  const { hideVehicleRecentActivity } = getSettings(state)

  return parseBooleanUserSetting(hideVehicleRecentActivity, {
    defaultValueWhenNotValid: false,
  })
}

export const getIsSubUser = (state: PermissionsAppState) => {
  const { isSubUser } = getSettings(state)

  return parseBooleanUserSetting(isSubUser, {
    defaultValueWhenNotValid: false,
  })
}

export const getDefaultCountry = (state: AppState) =>
  getSettings(state).defaultCountry as string

/**
 *  @deprecated
 *  Try to use `getUserIANATimezone` instead.
 */
export const getUserTimeZone = createSelector(
  getTimeZones,
  getSettings,
  (timeZones, settings = {}) => {
    const { timezoneSettings, defaultTimeZone, useLocalTime } = settings
    if (timezoneSettings) {
      return (timeZones ?? []).find((t) => t.id === settings.timeZone) || {}
    }
    if (useLocalTime) {
      return { ianaName: guessedLocalTimeZone }
    }
    if (defaultTimeZone) {
      return { ianaName: defaultTimeZone as string }
    }
    return { ianaName: guessedLocalTimeZone }
  },
)

/**
 *  @deprecated
 * Try to use `getUserIANATimezone` instead.
 */
export const getUserTimeZoneIANA = (state: AppState) =>
  (getUserTimeZone(state) as FixMeAny).ianaName

export const getPreferences = (state: AppState) => state.user.preferences

export const getStylePositionUnreliableTypeSetting = (state: AppState) =>
  getSettings(state).stylePositionUnreliableType as
    | 'visible'
    | 'visible-warning'
    | 'visible-warning-nomap'
    | 'hidden'

export const getRoadDescriptionTypeSetting = (state: AppState) =>
  (getSettings(state).roadDescriptionType as Login.RoadDescriptionTypeSetting) ??
  'principal'

export const getUserPositionAddressStateGetter = createSelector(
  getStylePositionUnreliableTypeSetting,
  getRoadDescriptionTypeSetting,
  (stylePositionUnreliableTypeSetting, roadDescriptionType) =>
    ({
      address: addressToMatch,
      gpsFixType,
    }: {
      address: PositionDescription
      gpsFixType: number | null
    }) =>
      match(addressToMatch)
        .returnType<
          | { visibility: 'PRIVATE' }
          | 'EMPTY'
          | {
              visibility: 'PUBLIC'
              type: 'standard'
              processedDescriptionText: string
            }
          | {
              visibility: 'PUBLIC'
              type: 'warning'
              processedDescriptionText: string
            }
          | {
              visibility: 'PUBLIC'
              type: 'noGPS'
              processedDescriptionText: string
            }
        >()
        .with({ visibility: 'PRIVATE' }, (privateObj) => privateObj)
        .with({ visibility: 'PUBLIC' }, null, (address) => {
          const isAnUnreliableLocation = isNil(gpsFixType)
            ? /* gpsFixType may not yet be defined for multiple reasons. We don't want to consider the address unreliable without enough data to confirm it.
               See https://cartrack.atlassian.net/browse/FTW-2928?focusedCommentId=565501 for more info
            */
              false
            : gpsFixType < 3

          const noGPSSignalObj = {
            visibility: 'PUBLIC',
            type: 'noGPS',
            processedDescriptionText: ctIntl.formatMessage({ id: 'No GPS Signal' }),
          } as const

          if (address === null) {
            return isAnUnreliableLocation ? noGPSSignalObj : 'EMPTY'
          }

          const publicProcessedDescriptionText = (() => {
            if (roadDescriptionType === 'principal') {
              return address.principal.description
            }

            // Even though the user wants to use the alternative, it might not yet be available on the DB so we fallback to principal
            return address.alternatives.description_al ?? address.principal.description
          })()

          const standardPositionDescription = {
            visibility: 'PUBLIC',
            type: 'standard',
            processedDescriptionText: publicProcessedDescriptionText,
          } as const

          if (isAnUnreliableLocation) {
            switch (stylePositionUnreliableTypeSetting) {
              case 'hidden': {
                return noGPSSignalObj
              }
              case 'visible-warning-nomap': {
                return {
                  visibility: 'PUBLIC',
                  type: 'noGPS',
                  processedDescriptionText: publicProcessedDescriptionText,
                } as const
              }
              case 'visible-warning': {
                return {
                  visibility: 'PUBLIC',
                  type: 'warning',
                  processedDescriptionText: publicProcessedDescriptionText,
                } as const
              }
              case 'visible': {
                return standardPositionDescription
              }
            }
          }

          return standardPositionDescription
        })
        .exhaustive(),
)

export const getDataUsageBuyMore = (state: AppState) => {
  const { dataUsageBuyMore } = getSettings(state)
  return parseBooleanUserSetting(dataUsageBuyMore, {
    defaultValueWhenNotValid: false,
  })
}

export const getArcgisApiKey = createSelectorWithStrictMode(
  getSettings,
  ({ arcgisApiKey }): string | undefined => {
    // eslint-disable-next-line no-console
    console.log('SETTING: arcgisApiKey : START PARSING raw:', arcgisApiKey)
    const parsed = (() => {
      if (isNonEmptyTrimmedString(arcgisApiKey)) {
        return arcgisApiKey
      }

      return undefined
    })()
    // eslint-disable-next-line no-console
    console.log('SETTING: arcgisApiKey : END PARSING parsed:', parsed)

    return parsed
  },
)

export const getArcgisBaseLayerToken = createSelectorWithStrictMode(
  getSettings,
  ({ arcgisBaseLayerToken }): string | undefined => {
    // eslint-disable-next-line no-console
    console.log(
      'SETTING: arcgisBaseLayerToken : START PARSING raw:',
      arcgisBaseLayerToken,
    )
    const parsed = (() => {
      if (isNonEmptyTrimmedString(arcgisBaseLayerToken)) {
        return arcgisBaseLayerToken
      }

      return undefined
    })()
    // eslint-disable-next-line no-console
    console.log('SETTING: arcgisBaseLayerToken : END PARSING parsed:', parsed)

    return parsed
  },
)

/**
 * IMPORTANT
 * We use `catch` on fields that are not required. This allows us to consider the layer option valid even if some optional fields are not valid.
 */
const arcgisFeatureLayerOptionSchema = z.object({
  url: z.string().min(1),
  fields: z.array(z.string()).optional().catch(undefined),
})
type ArcgisFeatureLayerOption = z.infer<typeof arcgisFeatureLayerOptionSchema>
export const parseArcgisFeatureLayersOptions = (
  rawLayerOptions: unknown,
): Array<ArcgisFeatureLayerOption> => {
  if (isNil(rawLayerOptions)) {
    return []
  }
  const layerOptions: unknown =
    typeof rawLayerOptions === 'string' ? JSON.parse(rawLayerOptions) : rawLayerOptions

  if (R.isArray(layerOptions)) {
    return Array_filterMap(layerOptions, (layerOption, { RemoveSymbol }) => {
      const parseResult = arcgisFeatureLayerOptionSchema.safeParse(layerOption)
      if (parseResult.success) {
        return parseResult.data
      }
      console.error('Invalid ArcgisFeatureLayerOption', parseResult.error)
      return RemoveSymbol
    })
  }
  return []
}
export const getArcgisFeatureLayersOptions = createSelectorWithStrictMode(
  getSettings,
  ({ arcgisFeatureLayersOptions }): Array<ArcgisFeatureLayerOption> => {
    // eslint-disable-next-line no-console
    console.log(
      'SETTING: arcgisFeatureLayersOptions : START PARSING raw:',
      arcgisFeatureLayersOptions,
    )
    const parsed = parseArcgisFeatureLayersOptions(arcgisFeatureLayersOptions)
    // eslint-disable-next-line no-console
    console.log('SETTING: arcgisFeatureLayersOptions : END PARSING parsed:', parsed)
    return parsed
  },
)

/**
 * IMPORTANT
 * We use `catch` on fields that are not required. This allows us to consider the layer option valid even if some optional fields are not valid.
 */
const arcgisDynamicMapLayerOption = z.object({
  url: z.string().min(1),
  opacity: z.number().optional().catch(undefined),
})
type ArcgisDynamicMapLayerOption = z.infer<typeof arcgisDynamicMapLayerOption>
export const parseArcgisDynamicMapLayersOptions = (
  rawLayerOptions: unknown,
): Array<ArcgisDynamicMapLayerOption> => {
  if (isNil(rawLayerOptions)) {
    return []
  }
  const layerOptions: unknown =
    typeof rawLayerOptions === 'string' ? JSON.parse(rawLayerOptions) : rawLayerOptions

  if (R.isArray(layerOptions)) {
    return Array_filterMap(layerOptions, (layerOption, { RemoveSymbol }) => {
      const parseResult = arcgisDynamicMapLayerOption.safeParse(layerOption)
      if (parseResult.success) {
        return parseResult.data
      }
      console.error('Invalid ArcgisDynamicMapLayersOption', parseResult.error)
      return RemoveSymbol
    })
  }
  return []
}
export const getArcgisDynamicMapLayersOptions = createSelectorWithStrictMode(
  getSettings,
  ({ arcgisDynamicMapLayersOptions }): Array<ArcgisDynamicMapLayerOption> => {
    // eslint-disable-next-line no-console
    console.log(
      'SETTING: arcgisDynamicMapLayersOptions : START PARSING raw:',
      arcgisDynamicMapLayersOptions,
    )
    const parsed = parseArcgisDynamicMapLayersOptions(arcgisDynamicMapLayersOptions)
    // eslint-disable-next-line no-console
    console.log('SETTING: arcgisDynamicMapLayersOptions : END PARSING parsed:', parsed)
    return parsed
  },
)

export type UserAvailableMapApiProvider = Tagged<
  MapApiProvider,
  'UserAvailableMapApiProvider'
>
export const getUserAvailableMapApiProviders = createSelectorWithStrictMode(
  getSettings,
  getDefaultMapApiProviderSetting,
  (
    { availableMapApiProviders },
    mapApiProviderSetting,
  ): NonEmptyArray<UserAvailableMapApiProvider> => {
    function parseAsArray(
      array: ReadonlyArray<unknown>,
    ): NonEmptyArray<UserAvailableMapApiProvider> {
      const finalArray: Array<MapApiProvider> = Array_filterMap(
        array,
        (item, { RemoveSymbol }) => {
          if (typeof item === 'string' && item.trim() === '') {
            return RemoveSymbol
          }
          if (R.isNullish(item)) {
            return RemoveSymbol
          }
          const trimmedItem = typeof item === 'string' ? item.trim() : item
          const number = Number(trimmedItem)
          if (Number.isNaN(number)) {
            return RemoveSymbol
          }
          return mapApiProviderSet.has(number as MapApiProvider)
            ? (number as MapApiProvider)
            : RemoveSymbol
        },
      )

      return Array_uniqBy(
        [baseProvider, ...finalArray],
        (i) => i,
      ) as NonEmptyArray<UserAvailableMapApiProvider>
    }

    const baseProvider = mapApiProviderSetting as UserAvailableMapApiProvider
    if (typeof availableMapApiProviders === 'string') {
      const string = availableMapApiProviders.trim()
      if (string === '') {
        return [baseProvider]
      }
      const array = string.split(',').map((s) => s.trim())

      return parseAsArray(array)
    }
    if (R.isArray(availableMapApiProviders)) {
      return parseAsArray(availableMapApiProviders)
    }

    return [baseProvider]
  },
)

export const getBlockLivestreamIfDataExceeded = (state: AppState) => {
  const { blockLivestreamIfDataExceeded } = getSettings(state)

  return parseBooleanUserSetting(blockLivestreamIfDataExceeded, {
    defaultValueWhenNotValid: true,
  })
}

export const getVisionWatchVideoOnDemandSetting = (state: PermissionsAppState) => {
  const { visionWatchVideoOnDemand } = getSettings(state)
  return parseBooleanUserSetting(visionWatchVideoOnDemand, {
    defaultValueWhenNotValid: false,
  })
}

export const getGenderLabel = (state: AppState): string => {
  const { genderLabel } = getSettings(state)
  if (isNilOrEmptyString(genderLabel)) {
    return 'Gender'
  }

  return genderLabel as string
}

export const getLandmarksDeleteLandmarkSetting = (state: AppState) => {
  const { landmarksDeleteLandmark } = getSettings(state)

  return parseBooleanUserSetting(landmarksDeleteLandmark, {
    defaultValueWhenNotValid: false,
  })
}

export const getDefaultVehiclesTableColumns = (
  state: AppState,
): Array<string> | null => {
  const { defaultVehiclesTableColumns } = getSettings(state)
  if (isNilOrEmptyString(defaultVehiclesTableColumns)) {
    return null
  }

  return JSON.parse(defaultVehiclesTableColumns as string) as Array<string>
}

export const getDefaultDriversTableColumns = (
  state: AppState,
): Array<string> | null => {
  const { defaultDriversTableColumns } = getSettings(state)
  if (isNilOrEmptyString(defaultDriversTableColumns)) {
    return null
  }

  return JSON.parse(defaultDriversTableColumns as string) as Array<string>
}

type ArrayWithAtLeastTwoElements<T> = [T, T, ...Array<T>]

export const getPrimaryEmailSetting = createSelectorWithStrictMode(
  getSettings,
  ({ primaryEmail }): string | ArrayWithAtLeastTwoElements<string> | null => {
    if (typeof primaryEmail !== 'string') {
      return null
    }
    const primaryEmailNoWs = primaryEmail.replaceAll(/\s+/g, '')
    if (primaryEmailNoWs.length === 0) {
      return null
    }

    const splittedEmails = primaryEmailNoWs.split(';')
    if (splittedEmails.length === 0) {
      return null
    }
    if (splittedEmails.length === 1) {
      return splittedEmails[0]
    }
    return splittedEmails as ArrayWithAtLeastTwoElements<string>
  },
)

export const getShowVehicleOverspeedThreshold = (state: AppState) => {
  const { showVehicleOverspeedThreshold } = getSettings(state)

  return parseBooleanUserSetting(showVehicleOverspeedThreshold, {
    defaultValueWhenNotValid: false,
  })
}

export const getUpdateVehicleOdometerPermission = (state: AppState) => {
  const { updateVehicleOdometer } = getSettings(state)

  return parseBooleanUserSetting(updateVehicleOdometer, {
    defaultValueWhenNotValid: false,
  })
}

export const getUpdateVehicleUnitClockPermission = (state: AppState) => {
  const { updateVehicleUnitClock } = getSettings(state)

  return parseBooleanUserSetting(updateVehicleUnitClock, {
    defaultValueWhenNotValid: false,
  })
}

export const getMaxAllActivityDaysSetting = (state: AppState): number | null => {
  const { maxAllActivityDays } = getSettings(state)
  const parsed = parseNumberUserSetting(maxAllActivityDays, {
    defaultValueWhenNotValid: null,
  })

  if (parsed === 0) {
    return null // In case the BE trolls and sends 0. It does not make sense so we return null
  }
  return parsed
}

export const getMaxDailyActivityDaysSetting = (state: AppState): number | null => {
  const { maxDailyActivityDays } = getSettings(state)
  const parsed = parseNumberUserSetting(maxDailyActivityDays, {
    defaultValueWhenNotValid: null,
  })

  return parsed
}

export const getContactEmailEnabled = (state: AppState) => {
  const { contactEmailEnabled } = getSettings(state)

  return parseBooleanUserSetting(contactEmailEnabled, {
    defaultValueWhenNotValid: true,
  })
}

export const getContactSmsEnabled = (state: AppState) => {
  const { contactSmsEnabled } = getSettings(state)

  return parseBooleanUserSetting(contactSmsEnabled, {
    defaultValueWhenNotValid: true,
  })
}

export const getAssignedDriversOnlyContact = (state: AppState) => {
  const { assignedDriversOnlyContact } = getSettings(state)

  return parseBooleanUserSetting(assignedDriversOnlyContact, {
    defaultValueWhenNotValid: true,
  })
}

export const getCanBuySMS = (state: AppState) => {
  const { canBuySMS } = getSettings(state)

  return parseBooleanUserSetting(canBuySMS, {
    defaultValueWhenNotValid: false,
  })
}

export const getShowTelegram = (state: AppState) => {
  const { showTelegram } = getSettings(state)

  return parseBooleanUserSetting(showTelegram, {
    defaultValueWhenNotValid: false,
  })
}

export const getTelegramContactsSetting = (state: PermissionsAppState) => {
  const { telegramContacts } = getSettings(state)

  return parseBooleanUserSetting(telegramContacts, {
    defaultValueWhenNotValid: false,
  })
}

export const getLineContactsSetting = (state: AppState) => {
  const { lineContacts } = getSettings(state)

  return parseBooleanUserSetting(lineContacts, {
    defaultValueWhenNotValid: false,
  })
}

/**
 * @deprecated - it's not encouraged to use this. Prefer using a specific setting for the feature.
 */
export const getIsAdminSetting = (state: AppState) => {
  const { isAdmin } = getSettings(state)
  return parseBooleanUserSetting(isAdmin, {
    defaultValueWhenNotValid: false,
  })
}

export const getCanImmobiliseVehicles = (state: AppState) => {
  // This is a temporary solution until BE correctly supports canImmobiliseVehicles, regardless of isAdmin or not
  const isAdmin = getIsAdminSetting(state)
  if (isAdmin) {
    return true
  }

  const { canImmobiliseVehicles } = getSettings(state)
  return parseBooleanUserSetting(canImmobiliseVehicles, {
    defaultValueWhenNotValid: false,
  })
}

export const getCustomFormPermissionsGuard = createSelectorWithStrictMode(
  getSettings,
  (settings) => ({
    canCreate: parseBooleanUserSetting(settings.customFormActionCreate, {
      defaultValueWhenNotValid: false,
    }),
    canDelete: parseBooleanUserSetting(settings.customFormActionDelete, {
      defaultValueWhenNotValid: false,
    }),
    canEdit: parseBooleanUserSetting(settings.customFormActionEdit, {
      defaultValueWhenNotValid: false,
    }),
  }),
)

export const getVehicleInspectionPermissionsGuard = createSelectorWithStrictMode(
  getSettings,
  (settings) => ({
    canApprove: parseBooleanUserSetting(settings.vehicleInspectionActionApprove, {
      defaultValueWhenNotValid: true, // Since atm the sub users can approve by default we set our default to true
    }),
  }),
)

// Currently only supports one default datetime locale, which is en-GB.
const dateTimeLocaleSchema = z.literal('en-GB').nullable()

export const getDefaultDateTimeLocale = (state: AppState): BCP47LanguageTag | null => {
  const { defaultDateTimeLocale } = getSettings(state)

  const parseResult = dateTimeLocaleSchema.safeParse(defaultDateTimeLocale)

  if (parseResult.success) {
    return parseResult.data
  }

  return null
}

export const getReportDataDurationLimitInMonths = (state: AppState) => {
  const { reportDataDurationLimitInMonths } = getSettings(state)
  const castToNumber = z.preprocess(Number, z.union([z.string(), z.number()]))
  const result = castToNumber.safeParse(reportDataDurationLimitInMonths)

  if (!result.success) {
    return 24 // Default value
  }

  return result.data as number
}

export const getReportRepeatIntervalLimitInMonths = (state: AppState) => {
  const { reportRepeatIntervalLimitInMonths } = getSettings(state)
  const castToNumber = z.preprocess(Number, z.union([z.string(), z.number()]))
  const result = castToNumber.safeParse(reportRepeatIntervalLimitInMonths)

  if (!result.success) {
    return 12 // Default value
  }

  return result.data as number
}

const vehicleGroupLabelSchema = z.literal('group').or(z.literal('site'))

export const getVehicleGroupLabel = (state: PermissionsAppState) => {
  const { vehicleGroupLabel } = getSettings(state)
  const parseResult = vehicleGroupLabelSchema.safeParse(vehicleGroupLabel)

  if (parseResult.success) {
    return match(parseResult.data)
      .with(
        'group',
        () =>
          ({
            groupLabel: 'Group',
            groupsLabel: 'Groups',
          }) as const,
      )
      .with(
        'site',
        () =>
          ({
            groupLabel: 'Site',
            groupsLabel: 'Sites',
          }) as const,
      )
      .exhaustive()
  }

  return {
    groupLabel: 'Group',
    groupsLabel: 'Groups',
  } as const
}

export const getDataMbCreditSetting = (state: AppState): number => {
  const { dataMbCredit } = getSettings(state)
  const parsed = parseNumberUserSetting(dataMbCredit, {
    defaultValueWhenNotValid: 4,
  })

  return parsed
}
export const getCreditUseOnVisionSetting = (state: AppState): number => {
  const { creditUseOnVision } = getSettings(state)
  const parsed = parseNumberUserSetting(creditUseOnVision, {
    defaultValueWhenNotValid: 256,
  })

  return parsed
}

export const getCarpoolAllowBackDateBooking = (state: AppState) => {
  const { carpoolAllowBackDateBooking } = getSettings(state)

  return parseBooleanUserSetting(carpoolAllowBackDateBooking, {
    defaultValueWhenNotValid: false,
  })
}

export const getJwtAccessToken = (state: Pick<AppState, 'user'>) =>
  state.user.jwtAccessToken

export const getAIChatApiUrl = createSelectorWithStrictMode(
  getSettings,
  ({ llmChatUrl }) => (isNonEmptyTrimmedString(llmChatUrl) ? llmChatUrl : null),
)

export const getCostsSetting = (state: AppState) => {
  const { costs } = getSettings(state)

  return parseBooleanUserSetting(costs, {
    defaultValueWhenNotValid: false,
  })
}

export const getManageRolesSetting = (state: AppState) => {
  const { userManageRoles } = getSettings(state)

  return parseBooleanUserSetting(userManageRoles, {
    defaultValueWhenNotValid: false,
  })
}

export const getControlRoomDismissIncidentSetting = (state: AppState) => {
  const { controlRoomDismissIncident } = getSettings(state)

  return parseBooleanUserSetting(controlRoomDismissIncident, {
    defaultValueWhenNotValid: false,
  })
}

export const getCoachingSetting = (state: AppState) => {
  const { coaching } = getSettings(state)

  return parseBooleanUserSetting(coaching, {
    defaultValueWhenNotValid: false,
  })
}

export const getDeliveryAllowAdminManagementAccess = (state: AppState) => {
  const { deliveryAllowAdminManagementAccess } = getSettings(state)

  return parseBooleanUserSetting(deliveryAllowAdminManagementAccess, {
    defaultValueWhenNotValid: false,
  })
}

export const getDriversDeactivateDriver = (state: AppState) => {
  const { driversDeactivateDriver } = getSettings(state)

  return parseBooleanUserSetting(driversDeactivateDriver, {
    defaultValueWhenNotValid: false,
  })
}

export const getVehiclesAddGroupSetting = (state: AppState) => {
  const { vehiclesAddGroup } = getSettings(state)

  return parseBooleanUserSetting(vehiclesAddGroup, {
    defaultValueWhenNotValid: false,
  })
}

export const getVehiclesEditVehicleSetting = (state: AppState) => {
  const { vehiclesEditVehicle } = getSettings(state)

  return parseBooleanUserSetting(vehiclesEditVehicle, {
    defaultValueWhenNotValid: false,
  })
}

export const getTrailersPermissionsSetting = (state: AppState) => {
  const { trailersAddTrailer, trailersEditTrailer, trailersDeleteTrailer } =
    getSettings(state)

  return {
    add: parseBooleanUserSetting(trailersAddTrailer, {
      defaultValueWhenNotValid: false,
    }),
    edit: parseBooleanUserSetting(trailersEditTrailer, {
      defaultValueWhenNotValid: false,
    }),
    delete: parseBooleanUserSetting(trailersDeleteTrailer, {
      defaultValueWhenNotValid: false,
    }),
  }
}

export const getVehiclesImportVehiclesToGroup = (state: AppState) => {
  const { vehiclesImportVehiclesToGroup } = getSettings(state)

  return parseBooleanUserSetting(vehiclesImportVehiclesToGroup, {
    defaultValueWhenNotValid: false,
  })
}

export const getIsRequiredMapSelectionAtSiteLocationsPage = (state: AppState) => {
  const { isRequiredMapSelectionAtSiteLocationsPage } = getSettings(state)

  return parseBooleanUserSetting(isRequiredMapSelectionAtSiteLocationsPage, {
    defaultValueWhenNotValid: true,
  })
}

export const getComputedPrivacyHideLocationsFromDay = (state: AppState) => {
  const { privacyHideVehiclesLocationDataFromToday } = getSettings(state)

  const privacyHideVehiclesLocationDataFromTodayValue = parseBooleanUserSetting(
    privacyHideVehiclesLocationDataFromToday,
    {
      defaultValueWhenNotValid: false,
    },
  )

  const privacyHideLocationsFromDayValue = getPrivacyHideLocationsFromDaySetting(state)

  if (
    privacyHideVehiclesLocationDataFromTodayValue &&
    privacyHideLocationsFromDayValue === 0
  ) {
    return -1 //hide vehicles' location data of today (it's negative number according to backend)
  }

  return privacyHideLocationsFromDayValue
}

export const getPrivacyHideLocationsFromDaySettingStoreless = ({
  privacyHideLocationsFromDay,
}: AppState['user']['settings']) =>
  isBETrue(privacyHideLocationsFromDay)
    ? -1 // based on the comment https://gitlab.cartrack.com/cartrack-fleet-dev/bitbucket-repos/projects/fleetapp-jsonrpc/-/issues/8949#note_192805
    : parseNumberUserSetting(privacyHideLocationsFromDay, {
        defaultValueWhenNotValid: 0,
      })

export const getPrivacyHideLocationsFromDaySetting = (state: AppState) =>
  getPrivacyHideLocationsFromDaySettingStoreless(getSettings(state))

export const getSignalRHubConnectionUrlStoreless = (
  userSettings: AppState['user']['settings'],
): string | null => {
  const { signalRHubConnectionUrl } = userSettings

  return isNonEmptyTrimmedString(signalRHubConnectionUrl)
    ? signalRHubConnectionUrl
    : null
}

export const getCurrentVehicleLivePositionStrategy = (state: AppState) =>
  state.user.currentVehicleLivePositionStrategy

export const getShowLivePositionsStoreless = (
  userSettings: AppState['user']['settings'],
) => getPrivacyHideLocationsFromDaySettingStoreless(userSettings) === 0

export const getShowLivePositions = (state: AppState) =>
  getShowLivePositionsStoreless(getSettings(state))

export const getCanSetupSignalRHubConnectionMetaStoreless = (
  userSettings: AppState['user']['settings'],
):
  | false
  | {
      connectionUrl: string
    } => {
  const showLivePositions = getShowLivePositionsStoreless(userSettings)
  const signalRHubConnectionUrl = getSignalRHubConnectionUrlStoreless(userSettings)
  const useWebSocketForLivePositions =
    getUseWebSocketForLivePositionsStoreless(userSettings)
  return showLivePositions && !!signalRHubConnectionUrl && useWebSocketForLivePositions
    ? { connectionUrl: signalRHubConnectionUrl }
    : false
}

export const getCanSetupSignalRHubConnectionMeta = (state: AppState) =>
  getCanSetupSignalRHubConnectionMetaStoreless(getSettings(state))

const getUseWebSocketForLivePositionsStoreless = (
  userSettings: AppState['user']['settings'],
): boolean => {
  const { useWebSocketForLivePositions } = userSettings
  return parseBooleanUserSetting(useWebSocketForLivePositions, {
    defaultValueWhenNotValid: false,
  })
}

export const getVehicleDetailsShowTerminalSerial = (state: AppState): boolean => {
  const { vehicleDetailsShowTerminalSerial } = getSettings(state)

  return parseBooleanUserSetting(vehicleDetailsShowTerminalSerial, {
    defaultValueWhenNotValid: false,
  })
}

export const getVisionManageLimits = (state: AppState) => {
  const { visionManageLimits } = getSettings(state)

  return parseBooleanUserSetting(visionManageLimits, {
    defaultValueWhenNotValid: false,
  })
}

export const getVisionBuyData = (state: AppState) => {
  const { visionBuyData } = getSettings(state)

  return parseBooleanUserSetting(visionBuyData, {
    defaultValueWhenNotValid: false,
  })
}

export const getNpsScorePopupEnable = (state: AppState) => {
  const { npsScorePopupEnable } = getSettings(state)

  return parseBooleanUserSetting(npsScorePopupEnable, {
    defaultValueWhenNotValid: false,
  })
}

export const getRucorReportId = (state: AppState) => {
  const { rucorReportId } = getSettings(state)

  const parsedResult = reportIdSchema.safeParse(rucorReportId)

  if (parsedResult.success) {
    return parsedResult.data
  }

  return null
}

export const getReportPermissions = createSelectorWithStrictMode(
  getSettings,
  (settings) => {
    const {
      reportsCustomize,
      reportsSetup,
      reportsSetupDownloadReport,
      reportsFavorites,
      reportsInformation,
      reportsProfiles,
    } = settings

    return {
      /**
       * Permission to access reports 'All reports' page
       */
      setup: parseBooleanUserSetting(reportsSetup, {
        defaultValueWhenNotValid: false,
      }),
      /**
       * Permission to download reports
       */
      download: parseBooleanUserSetting(reportsSetupDownloadReport, {
        defaultValueWhenNotValid: false,
      }),
      /**
       * Permission to access reports favorites tab
       */
      favorites: parseBooleanUserSetting(reportsFavorites, {
        defaultValueWhenNotValid: false,
      }),
      /**
       * Permission to access reports 'Status and Management' page
       */
      information: parseBooleanUserSetting(reportsInformation, {
        defaultValueWhenNotValid: false,
      }),
      /**
       * Permission to access reports custom tab
       */
      customize: parseBooleanUserSetting(reportsCustomize, {
        defaultValueWhenNotValid: false,
      }),
      /**
       * Permission to access reports profiles tab
       */
      profiles: parseBooleanUserSetting(reportsProfiles, {
        defaultValueWhenNotValid: false,
      }),
    }
  },
)

export const getSensorPermission = createSelectorWithStrictMode(
  getSettings,
  (settings) => {
    const { enableFridgeSensor, enableFuelSensor, enableOtherSensors } = settings

    return {
      fridge: parseBooleanUserSetting(enableFridgeSensor, {
        defaultValueWhenNotValid: false,
      }),
      fuel: parseBooleanUserSetting(enableFuelSensor, {
        defaultValueWhenNotValid: false,
      }),
      other: parseBooleanUserSetting(enableOtherSensors, {
        defaultValueWhenNotValid: false,
      }),
    }
  },
)

export const getMapTripDownloadPermission = (state: AppState) => {
  const { mapEnableTripDownload } = getSettings(state)

  return parseBooleanUserSetting(mapEnableTripDownload, {
    defaultValueWhenNotValid: false,
  })
}

export const getCarpoolAddVehicleCategories = (state: AppState) => {
  const { carpoolAddVehicleCategoriesEnabled } = getSettings(state)

  return parseBooleanUserSetting(carpoolAddVehicleCategoriesEnabled, {
    defaultValueWhenNotValid: false,
  })
}

export const getCarpoolEditVehicleCategories = (state: AppState) => {
  const { carpoolEditVehicleCategoriesEnabled } = getSettings(state)

  return parseBooleanUserSetting(carpoolEditVehicleCategoriesEnabled, {
    defaultValueWhenNotValid: false,
  })
}

export const getCarpoolDeleteVehicleCategories = (state: AppState) => {
  const { carpoolDeleteVehicleCategoriesEnabled } = getSettings(state)

  return parseBooleanUserSetting(carpoolDeleteVehicleCategoriesEnabled, {
    defaultValueWhenNotValid: false,
  })
}

export const getTachographWindowsAppUrl = (state: AppState) => {
  const { tachographWindowsAppUrl } = getSettings(state)

  if (typeof tachographWindowsAppUrl !== 'string') {
    return null
  }

  return tachographWindowsAppUrl
}

export const getAllowRoadSpeed = (state: AppState) => {
  const { roadSpeed } = getSettings(state)

  return parseBooleanUserSetting(roadSpeed, {
    defaultValueWhenNotValid: false,
  })
}
export const getAlertsNew = (state: AppState) => {
  const { alertsNew } = getSettings(state)

  return parseBooleanUserSetting(alertsNew, {
    defaultValueWhenNotValid: false,
  })
}

export const getAlertsMessageCreditsPageAccess = (state: AppState) => {
  const { alertsSms } = getSettings(state)

  return parseBooleanUserSetting(alertsSms, {
    defaultValueWhenNotValid: false,
  })
}
export const getAlertsMessageCreditsEditLimit = (state: AppState) => {
  const { messageCreditsEditLimits } = getSettings(state)

  return parseBooleanUserSetting(messageCreditsEditLimits, {
    defaultValueWhenNotValid: false,
  })
}

export const getImportReminderPermission = (state: AppState) => {
  const { enableImportReminders } = getSettings(state)

  return parseBooleanUserSetting(enableImportReminders, {
    defaultValueWhenNotValid: false,
  })
}

export const getUser = (state: PermissionsAppState) => state.user.user

const consoleErrorOnceLoggedSet = new Set<string>()
const consoleErrorOnce = (msg: string) => {
  if (consoleErrorOnceLoggedSet.has(msg)) {
    return
  }

  console.error(msg)
  consoleErrorOnceLoggedSet.add(msg)
}

export const doesCurrentUserHaveAccessFromSetting = (
  state: PermissionsAppState,
  /** Access setting with bool value */
  setting: keyof Settings.UserSettingsRaw,
) => {
  const isCurrentUserAuthenticated = !isEmpty(getUser(state))

  if (isCurrentUserAuthenticated) {
    const settings = getSettings(state)
    const value = settings[setting]

    let errorMessage
    if (isEmpty(settings) === false) {
      if (value === undefined) {
        errorMessage = `[Cartrack] - There is no setting with name ${JSON.stringify(
          setting,
        )}`
      } else if (isBoolean(value) === false) {
        errorMessage = `[Cartrack] - Access type settings should be of type bool. The value for setting ${JSON.stringify(
          setting,
        )} was ${JSON.stringify(value)}`
      }
    }

    if (errorMessage !== undefined) {
      // It will be logged to sentry but not crash the app since it's production
      consoleErrorOnce(errorMessage)
    }

    return value === true
  }

  return false
}

const getFacilityModuleNameTypeSetting = (
  state: PermissionsAppState,
): 'facility' | 'unit' | 'location' | 'site' => {
  const { facilityModuleNameType } = getSettings(state)

  switch (facilityModuleNameType) {
    case 'unit': {
      return 'unit'
    }
    case 'location': {
      return 'location'
    }
    case 'site': {
      return 'site'
    }
    default: {
      return 'location'
    }
  }
}

export const translateFacilitiesTermFunc = (
  term: FacilityTranslationTermsBanned,
  facilityModuleNameType: ReturnType<typeof getFacilityModuleNameTypeSetting>,
): string => {
  switch (term) {
    case 'global.module.facilities': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'global.module.facilities')
          .with('unit', () => 'global.module.facilities.dynamicVariant.unit')
          .with('location', () => 'global.module.facilities.dynamicVariant.location')
          .with('site', () => 'global.module.facilities.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'facilities.addFacility': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'facilities.addFacility')
          .with('unit', () => 'facilities.addFacility.dynamicVariant.unit')
          .with('location', () => 'facilities.addFacility.dynamicVariant.location')
          .with('site', () => 'facilities.addFacility.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'facilities.createFacility.success': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'facilities.createFacility.success')
          .with('unit', () => 'facilities.createFacility.success.dynamicVariant.unit')
          .with(
            'location',
            () => 'facilities.createFacility.success.dynamicVariant.location',
          )
          .with('site', () => 'facilities.createFacility.success.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'facilities.updateFacility.success': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'facilities.updateFacility.success')
          .with('unit', () => 'facilities.updateFacility.success.dynamicVariant.unit')
          .with(
            'location',
            () => 'facilities.updateFacility.success.dynamicVariant.location',
          )
          .with('site', () => 'facilities.updateFacility.success.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'facility.edit.title': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'facility.edit.title')
          .with('unit', () => 'facility.edit.title.dynamicVariant.unit')
          .with('location', () => 'facility.edit.title.dynamicVariant.location')
          .with('site', () => 'facility.edit.title.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'Facility': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'Facility')
          .with('unit', () => 'Facility.dynamicVariant.unit')
          .with('location', () => 'Facility.dynamicVariant.location')
          .with('site', () => 'Facility.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'facility.details': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'facility.details')
          .with('unit', () => 'facility.details.dynamicVariant.unit')
          .with('location', () => 'facility.details.dynamicVariant.location')
          .with('site', () => 'facility.details.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'controlroom.headers.facility': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'controlroom.headers.facility')
          .with('unit', () => 'controlroom.headers.facility.dynamicVariant.unit')
          .with(
            'location',
            () => 'controlroom.headers.facility.dynamicVariant.location',
          )
          .with('site', () => 'controlroom.headers.facility.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'facilities.deleteDialog.title': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'facilities.deleteDialog.title')
          .with('unit', () => 'facilities.deleteDialog.title.dynamicVariant.unit')
          .with(
            'location',
            () => 'facilities.deleteDialog.title.dynamicVariant.location',
          )
          .with('site', () => 'facilities.deleteDialog.title.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'facilities.deleteDialog.areYouSure.one': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'facilities.deleteDialog.areYouSure.one')
          .with(
            'unit',
            () => 'facilities.deleteDialog.areYouSure.one.dynamicVariant.unit',
          )
          .with(
            'location',
            () => 'facilities.deleteDialog.areYouSure.one.dynamicVariant.location',
          )
          .with(
            'site',
            () => 'facilities.deleteDialog.areYouSure.one.dynamicVariant.site',
          )
          .exhaustive(),
      })
    }
    case 'facilities.deleteDialog.areYouSure.other': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'facilities.deleteDialog.areYouSure.other')
          .with(
            'unit',
            () => 'facilities.deleteDialog.areYouSure.other.dynamicVariant.unit',
          )
          .with(
            'location',
            () => 'facilities.deleteDialog.areYouSure.other.dynamicVariant.location',
          )
          .with(
            'site',
            () => 'facilities.deleteDialog.areYouSure.other.dynamicVariant.site',
          )
          .exhaustive(),
      })
    }
    case 'facilities.deleteDialog.success': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'facilities.deleteDialog.success')
          .with('unit', () => 'facilities.deleteDialog.success.dynamicVariant.unit')
          .with(
            'location',
            () => 'facilities.deleteDialog.success.dynamicVariant.location',
          )
          .with('site', () => 'facilities.deleteDialog.success.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'All Facilities': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'All Facilities')
          .with('unit', () => 'All Facilities.dynamicVariant.unit')
          .with('location', () => 'All Facilities.dynamicVariant.location')
          .with('site', () => 'All Facilities.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'facilities.assignDevices.list.title': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'facilities.assignDevices.list.title')
          .with('unit', () => 'facilities.assignDevices.list.title.dynamicVariant.unit')
          .with(
            'location',
            () => 'facilities.assignDevices.list.title.dynamicVariant.location',
          )
          .with('site', () => 'facilities.assignDevices.list.title.dynamicVariant.site')
          .exhaustive(),
      })
    }
    case 'facilities.assignDevices.uploadBox.title': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'facilities.assignDevices.uploadBox.title')
          .with(
            'unit',
            () => 'facilities.assignDevices.uploadBox.title.dynamicVariant.unit',
          )
          .with(
            'location',
            () => 'facilities.assignDevices.uploadBox.title.dynamicVariant.location',
          )
          .with(
            'site',
            () => 'facilities.assignDevices.uploadBox.title.dynamicVariant.site',
          )
          .exhaustive(),
      })
    }
    case 'vehicleDetail.defaultFacility': {
      return ctIntl.formatMessage({
        id: match(facilityModuleNameType)
          .with('facility', () => 'vehicleDetail.defaultFacility')
          .with('unit', () => 'vehicleDetail.defaultFacility.dynamicVariant.unit')
          .with(
            'location',
            () => 'vehicleDetail.defaultFacility.dynamicVariant.location',
          )
          .with('site', () => 'vehicleDetail.defaultFacility.dynamicVariant.site')
          .exhaustive(),
      })
    }
  }
}

export const getFacilitiesTranslatorFn = createSelectorWithStrictMode(
  getFacilityModuleNameTypeSetting,
  (facilityModuleNameType) => ({
    translateFacilitiesTerm: (term: FacilityTranslationTermsBanned) =>
      translateFacilitiesTermFunc(term, facilityModuleNameType),
  }),
)

/**
 * __IMPORTANT__ Only use this if you are sure this is not sent to formatMessage.
 * Otherwise, we risk translations being used when they should not
 */
export const getFacilitiesTranslatedModuleName = (state: PermissionsAppState): string =>
  getFacilitiesTranslatorFn(state).translateFacilitiesTerm('global.module.facilities')

export const LOGGED_OUT = 'LOGGED_OUT'

export const doesCurrentUserSeeNewMiFleetImports = (state: PermissionsAppState) =>
  getSettings_UNSAFE(state).mifleetImportDataV2

export const getVisionDataManagementLimitPct = (state: AppState): number => {
  const { visionDataManagementLimitPct } = getSettings(state)
  const parsed = parseNumberUserSetting(visionDataManagementLimitPct, {
    defaultValueWhenNotValid: 0.8,
  })

  return parsed
}

export const getCreditsPageAccess = (state: AppState): boolean => {
  const { credits } = getSettings(state)

  return parseBooleanUserSetting(credits, {
    defaultValueWhenNotValid: false,
  })
}

export const getCanBuyCredits = (state: AppState): boolean => {
  const { canBuyCredits } = getSettings(state)

  return parseBooleanUserSetting(canBuyCredits, {
    defaultValueWhenNotValid: false,
  })
}

export const getCreditsLimitsChange = (state: AppState): boolean => {
  const { creditsLimitsChange } = getSettings(state)

  return parseBooleanUserSetting(creditsLimitsChange, {
    defaultValueWhenNotValid: false,
  })
}

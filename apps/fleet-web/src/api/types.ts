import { String_replace, String_startsWith } from '@karoo/utils'
import type { Tagged } from 'type-fest'
import { z } from 'zod/v4'

import { brandedEntityIdSchema } from 'src/util-functions/zod-utils'

import type { DriverLinkingMethod } from './vehicles/useVehicleDetailsQuery'

export const httpUrlSchema = z.url()

export const vehicleTripIdSchema = brandedEntityIdSchema<'VehicleTripId'>()
export type VehicleTripId = z.infer<typeof vehicleTripIdSchema>

export const bookingPurposeIdSchema = brandedEntityIdSchema<'BookingPurposeId'>()
export type BookingPurposeId = z.infer<typeof bookingPurposeIdSchema>

export const driverLicenseTypeIdSchema = brandedEntityIdSchema<'DriverLicenseTypeId'>()
export type DriverLicenseTypeId = z.infer<typeof driverLicenseTypeIdSchema>

export const driverSpecialLicenseTypeIdSchema =
  brandedEntityIdSchema<'DriverSpecialLicenseTypeId'>()
export type DriverSpecialLicenseTypeId = z.infer<
  typeof driverSpecialLicenseTypeIdSchema
>

/**
 * @deprecated This is currently in use because of the way BE is setup.
 * This is only somewhat valid for drivers, so they can add a special license number and a None type, when cartrack has not added their type yet.
 * For vehicles, this is not valid at all.
 */
export const DriverSpecialLicenseNoneId = '-1' as const

export const bookingVehicleCategoryIdSchema =
  brandedEntityIdSchema<'BookingVehicleCategoryId'>()
/**
 * Also known as `booking_vehicle_type_id` in the BE.
 */
export type BookingVehicleCategoryId = z.infer<typeof bookingVehicleCategoryIdSchema>

export const locationIdSchema = brandedEntityIdSchema<'LocationId'>()
/**
 * Also known as `site_location_id` in the BE.
 * Also known as "Facility".
 */
export type LocationId = z.infer<typeof locationIdSchema>

export const vehicleIdSchema = brandedEntityIdSchema<'VehicleId'>()
export type VehicleId = z.infer<typeof vehicleIdSchema>

export const terminalSerialSchema = z
  .string()
  .min(1)
  .brand<'TerminalSerial'>()
  .nullable()
export type TerminalSerial = z.infer<typeof terminalSerialSchema>

export const vehicleInspectionIdStandardSchema =
  brandedEntityIdSchema<'VehicleInspectionIdStandard'>()
export type VehicleInspectionIdStandard = z.infer<
  typeof vehicleInspectionIdStandardSchema
>

export const vehicleInspectionIdSubmittSchema =
  brandedEntityIdSchema<'VehicleInspectionIdSubmit'>()
export type VehicleInspectionIdSubmit = z.infer<typeof vehicleInspectionIdSubmittSchema>

export const vehicleInspectionIdCustomSchema =
  brandedEntityIdSchema<'VehicleInspectionIdCustom'>()
export type VehicleInspectionIdCustom = z.infer<typeof vehicleInspectionIdCustomSchema>

export const driverIdSchema = brandedEntityIdSchema<'DriverId'>()
export type DriverId = z.infer<typeof driverIdSchema>

export const driverGroupIdSchema = brandedEntityIdSchema<'DriverGroupId'>()
export type DriverGroupId = z.infer<typeof driverGroupIdSchema>

export const reportIdSchema = brandedEntityIdSchema<'ReportId'>()
export type ReportId = z.infer<typeof reportIdSchema>

export const reportFileFormatExtensionSchema = z
  .literal('pdf')
  .or(z.literal('xls').or(z.literal('csv')).or(z.literal('xlsx')))

export type ReportFileFormatType = z.infer<typeof reportFileFormatExtensionSchema>

export const vehicleGroupIdSchema = brandedEntityIdSchema<'VehicleGroupId'>()
export type VehicleGroupId = z.infer<typeof vehicleGroupIdSchema>

export const emailSchema = z.email()
export type EmailType = z.infer<typeof emailSchema>

export const geofenceIdSchema = brandedEntityIdSchema<'GeofenceId'>()
export type GeofenceId = z.infer<typeof geofenceIdSchema>

export const geofenceSystemIdSchema = brandedEntityIdSchema<'GeofenceSystemId'>()
export type GeofenceSystemId = z.infer<typeof geofenceSystemIdSchema>

export const landmarkIdSchema = brandedEntityIdSchema<'LandmarkId'>()
export type LandmarkId = z.infer<typeof landmarkIdSchema>

export const geofenceGroupIdSchema = brandedEntityIdSchema<'GeofenceGroupId'>()
export type GeofenceGroupId = z.infer<typeof geofenceGroupIdSchema>

export const geofenceVisitIdSchema = brandedEntityIdSchema<'GeofenceVisitId'>()
export type GeofenceVisitId = z.infer<typeof geofenceVisitIdSchema>

export const companyDepartmentIdSchema = brandedEntityIdSchema<'CompanyDepartmentId'>()
export type CompanyDepartmentId = z.infer<typeof companyDepartmentIdSchema>

export const customFormIdSchema = brandedEntityIdSchema<'CustomFormId'>()
export type CustomFormId = z.infer<typeof customFormIdSchema>

export const scoreCardScoreCountRuleIdSchema =
  brandedEntityIdSchema<'ScoreCardScoreCountRuleId'>()
export type ScoreCardScoreCountRuleId = z.infer<typeof scoreCardScoreCountRuleIdSchema>

export const scoreCardRefreshPeriodIdSchema =
  brandedEntityIdSchema<'ScoreCardRefreshPeriodId'>()
export type ScoreCardRefreshPeriodId = z.infer<typeof scoreCardRefreshPeriodIdSchema>

export const scoreCardWeightageIdSchema =
  brandedEntityIdSchema<'ScoreCardWeightageId'>()
export type ScoreCardWeightageId = z.infer<typeof scoreCardWeightageIdSchema>

export const aiConversationIdSchema = brandedEntityIdSchema<'AIConversationId'>()
export type AIConversationId = z.infer<typeof aiConversationIdSchema>

export const mifleetReportReferredNameSchema =
  brandedEntityIdSchema<'MifleetReportReferredName'>()
export type MifleetReportReferredName = z.infer<typeof mifleetReportReferredNameSchema>

export const bookingCancellationReasonIdSchema =
  brandedEntityIdSchema<'BookingCancellationReasonId'>()
export type BookingCancellationReasonId = z.infer<
  typeof bookingCancellationReasonIdSchema
>

export const driverScoresComparisonGroupIdSchema =
  brandedEntityIdSchema<'DriverScoresComparisonGroupId'>()
export type DriverScoresComparisonGroupId = z.infer<
  typeof driverScoresComparisonGroupIdSchema
>

export const calendarShortcutIdSchema = brandedEntityIdSchema<'CalendarShortcutId'>()
export type CalendarShortcutId = z.infer<typeof calendarShortcutIdSchema>

/**
 * Can be used in lists that can contain vehicles, vehicle groups, vehicle types, etc. Where the id needs to be unique across different entities
 */
export const globalGroupIdPrefix = 'g-' as const

const createGPrefixedId = <Id extends string>() =>
  z
    .string()
    .refine(
      (val) =>
        val.startsWith(globalGroupIdPrefix) &&
        /* after the g prefix we should have at least one char */
        val.length >= 3,
    )
    .transform((val) => val as `${typeof globalGroupIdPrefix}${Id}`) as z.ZodType<
    `${typeof globalGroupIdPrefix}${Id}`,
    `${typeof globalGroupIdPrefix}${Id}`
  >

export const vehicleGroupIdWithGPrefixSchema = createGPrefixedId<VehicleGroupId>()
export type VehicleGroupIdWithGPrefix = z.infer<typeof vehicleGroupIdWithGPrefixSchema>

export const getVehicleGroupIdFromIdWithGPrefix = (value: VehicleGroupIdWithGPrefix) =>
  value.replace(globalGroupIdPrefix, '') as VehicleGroupId
export const isVehicleGroupIdWithGPrefix = (
  value: string,
): value is VehicleGroupIdWithGPrefix => {
  const result = vehicleGroupIdWithGPrefixSchema.safeParse(value)
  return result.success
}

/**
 * Vehicle Type id
 */

export const globalVehicleTypePrefix = 'vehicleType-' as const
export const vehicleTypeGlobalIdSchema =
  z.custom<`${typeof globalVehicleTypePrefix}${VehicleType}`>((value) => {
    if (typeof value !== 'string') {
      return false
    }

    const prefix = globalVehicleTypePrefix
    if (!String_startsWith(value, prefix)) {
      return false
    }
    const vehicleType = String_replace(value, prefix, '')
    return vehicleTypeSchema.safeParse(vehicleType).success
  })

export type VehicleTypeGlobalId = z.infer<typeof vehicleTypeGlobalIdSchema>

/**
 * Driver group id
 */
export const driverGroupIdWithGPrefixSchema = createGPrefixedId<DriverGroupId>()
export type DriverGroupIdWithGPrefix = z.infer<typeof driverGroupIdWithGPrefixSchema>

export const getDriverGroupIdFromIdWithGPrefix = (value: DriverGroupIdWithGPrefix) =>
  value.replace(globalGroupIdPrefix, '') as DriverGroupId

export const isDriverGroupIdWithGPrefix = (
  value: string,
): value is DriverGroupIdWithGPrefix => {
  const result = driverGroupIdWithGPrefixSchema.safeParse(value)
  return result.success
}

export function createPrefixedIdSchema<
  const Prefix extends string,
  const IdType extends string,
>(prefix: Prefix) {
  return z.custom<`${Prefix}${IdType}`>((value) => {
    if (typeof value !== 'string') {
      return false
    }
    if (!String_startsWith(value, prefix)) {
      return false
    }
    const id = String_replace(value, prefix, '')
    return z.string().min(1).safeParse(id).success
  })
}

/**
 * Vehicle ID as returned by endpoints.
 */
export type ApiOutputVehicleId = Tagged<`${number}` | number, 'ApiOutputVehicleId'>
export type ApiOutputDriverId = Tagged<`${number}` | number, 'ApiOutputDriverId'>

/**
 * Main or Admin User ID
 */
export const mainUserIdSchema = brandedEntityIdSchema<'MainUserId'>()
export type MainUserId = z.infer<typeof mainUserIdSchema>
/**
 * Can be used in lists with both sub users and main users without possible collisions
 */
export type GlobalMainUserId = `mainUser:${MainUserId}`

/**
 * Sub User ID
 */
export const clientUserIdSchema = brandedEntityIdSchema<'ClientUserId'>()
export type ClientUserId = z.infer<typeof clientUserIdSchema>

/**
 * Can be used in lists with both sub users and main users without possible collisions
 */
export type GlobalClientUserId = `subUser:${ClientUserId}`

export type GlobalUserId = GlobalClientUserId | GlobalMainUserId

export const terminalEventIdSchema = brandedEntityIdSchema<'TerminalEventId'>()
export type TerminalEventId = z.infer<typeof terminalEventIdSchema>

export const videoRequestIdSchema = brandedEntityIdSchema<'VideoRequestId'>()
export type VideoRequestId = z.infer<typeof videoRequestIdSchema>

export const terminalEventTypeIdSchema = brandedEntityIdSchema<'TerminalEventTypeId'>()
export type TerminalEventTypeId = z.infer<typeof terminalEventTypeIdSchema>

export const terminalEventTypeCodeSchema = z.string().brand<'TerminalEventTypeCode'>()
/**
 * Represents event type codes on the DB, e.g: v_fatigue, ev_ign_on, v_cam_covered, etc.
 * We create a branded type to avoid mistakes and making sure devs use the correct variables in certain cases.
 */
export type TerminalEventTypeCode = z.infer<typeof terminalEventTypeCodeSchema>

export type VisionCameraType = 'HOWEN'
export type VehicleVisionCameraStatus =
  | 'ACTIVE'
  | 'INACTIVE'
  | 'NOT_INSTALLED'
  | 'NO_ACCESS'
  | 'CAN_NOT_WATCH_STREAM'
  | 'STANDBY'

export type GPSFixType = null | 0 | 1 | 2 | 3 | 4 | 5

type PositionDescriptionAlternatives = {
  description_al: string | null
}

export type UNSAFE_PositionDescription =
  | {
      principal: {
        description: string | null
      }
      alternatives: PositionDescriptionAlternatives
    }
  | null
  | undefined

export type PositionDescription =
  | {
      visibility: 'PRIVATE'
    }
  | {
      visibility: 'PUBLIC'
      principal: {
        description: string
      }
      alternatives: PositionDescriptionAlternatives
    }
  | null

export const DriverNameVisibilityStatus = {
  UNDISCLOSED: 'UNDISCLOSED',
  PUBLIC: 'PUBLIC',
} as const
export type DriverNameVisibilityStatus =
  (typeof DriverNameVisibilityStatus)[keyof typeof DriverNameVisibilityStatus]

export const driverNameSchema = z
  .object({ status: z.literal(DriverNameVisibilityStatus.UNDISCLOSED) })
  .or(
    z.object({
      name: z.string().nullable(),
      status: z.literal(DriverNameVisibilityStatus.PUBLIC),
    }),
  )

export type DriverName = z.infer<typeof driverNameSchema>

export type DriverInfo = {
  name: string | null
  status: DriverNameVisibilityStatus
  client_driver_id: DriverId | null
  linkage_type_enum: DriverLinkingMethod | null
  logo_image_base64: string | null
  is_default_driver?: unknown
}
export const VehicleType = {
  Default: '0',
  Motorbike: '1',
  SmallCar: '2',
  SedanCar: '3',
  FourByFour: '4',
  Van: '5',
  SmallTruck: '6',
  LargeTruck: '7',
  SmallMachine: '8',
  LargeMachine: '9',
  Bus: '10',
  GolfCart: '11',
  TruckConcretePump: '12',
  TruckMixer: '13',
  MobileCrane: '14',
  Boat: '15',
  Generator: '16',
  Crane: '17',
  StaticPump: '18',
  Trailer: '19',
  WifiUnit: '20',
  PickupTruck: '21',
  Ambulance: '22',
  WaterTruck: '23',
  FireTruck: '24',
  RoadRoller: '25',
  Grader: '26',
  Forklift: '27',
  Tractor: '28',
  DumpTruck: '29',
  Backhoe: '30',
  Loader: '31',
  Lorry: '32',
  LorryCrane: '33',
  TowTruck: '34',
  Uncharacterized: '35',
  Patrol: '36',
  PrisonerTransport: '37',
  BulletProof: '38',
  Jetski: '39',
  RailwayCar: '40',
  DrillingRig: '41',
  RTXMachine: '42',
  SkidSteer: '43',
  Escavator: '44',
} as const
export type VehicleType = (typeof VehicleType)[keyof typeof VehicleType]
const _vehicleTypeValues = Object.values(VehicleType)
export const vehicleTypeSchema = z.custom<VehicleType>((value) =>
  _vehicleTypeValues.includes(value as VehicleType),
)

/**
 * Translation ids coming from the BE or DB that we will parse through our translation system (that fetches from POEditor)
 * You can look at POEditor and search for backend. to see all the translations that are available
 *
 * NOTE: There can be exceptions. There might be an endpoint that returns a translation id that starts with other prefix (should be RARE)
 */
export type Standard_BE_TranslationId = `backEnd.${string}` | `backend.${string}`

export type TranslationOrCustomStringMetaObject =
  | {
      type: 'translationId'
      translationId: string
    }
  | {
      type: 'custom'
      customString: string
    }

export declare namespace Settings {
  type UserSettingsRaw = Partial<
    Record<
      | 'address'
      | 'admin'
      | 'adminCustomers'
      | 'adminDeviceStatus'
      | 'adminGeofence'
      | 'adminGroups'
      | 'adminLabels'
      | 'adminMiniTracker'
      | 'adminPoi'
      | 'adminPoiStatus'
      | 'adminReminders'
      | 'adminRemindersCustomCategoriesLimit'
      | 'adminSubUserSettings'
      | 'adminUserSettings'
      | 'adminVehicles'
      | 'adminVehicleSensors'
      | 'alerts'
      | 'alertsActive'
      | 'alertsNew'
      | 'alertsReminders'
      | 'alertsSetup'
      | 'alertsSms'
      | 'alertsWhatsapp'
      | 'alertUnitContact'
      | 'allowDeviceTracking'
      | 'allowFullVehicleEditable'
      | 'allowRegUpdate'
      | 'altdatasource'
      | 'apiKey'
      | 'apiSettingsTab'
      | 'appStoreLink'
      | 'arcgisApiKey'
      | 'arcgisBaseLayerToken'
      | 'arcgisDynamicMapLayersOptions'
      | 'arcgisFeatureLayersOptions'
      | 'assignedDriversOnlyContact'
      | 'availableMapApiProviders'
      | 'basicLogin'
      | 'blockLivestreamIfDataExceeded'
      | 'canBuyCredits'
      | 'canBuySMS'
      | 'canImmobiliseVehicles'
      | 'carpool'
      | 'carpoolAllowBackDateBooking'
      | 'carpoolAppName'
      | 'carpoolEnableCalendar'
      | 'carpoolEnableResources'
      | 'carpoolEnableSettings'
      | 'cell_number'
      | 'city'
      | 'coaching'
      | 'coachingCanAssignDrivers'
      | 'coachingDashboard'
      | 'coachingEventsColumnMenuItemDriversPreset'
      | 'coachingShowNoCoachingNeededButton'
      | 'coachingTemplate'
      | 'coachingVehicleHoursToCheckForInactivity'
      | 'communicatorCustomTripType'
      | 'communicatorWorkEndHour'
      | 'communicatorWorkStartHour'
      | 'carpoolRevamp'
      | 'companyName'
      | 'contactEmailEnabled'
      | 'contactSmsEnabled'
      | 'contactSupportEmail'
      | 'contactUsEmail'
      | 'contactUsNumber'
      | 'contactUsVasStartPrevent'
      | 'alertsRequestFootage'
      | 'controlRoom'
      | 'controlRoomContact'
      | 'controlRoomDismissIncident'
      | 'controlRoomNewIncidentsNotificationType'
      | 'costs'
      | 'costsApi'
      | 'costsToken'
      | 'countryCodeAlpha3'
      | 'countryLicenseState'
      | 'creditUseOnVision'
      | 'credits'
      | 'creditsLimitsChange'
      | 'ctCountries'
      | 'currencyIso'
      | 'currencySymbol'
      | 'customData'
      | 'customFormActionCreate'
      | 'customFormActionDelete'
      | 'customFormActionEdit'
      | 'customFormsTab'
      | 'daimlerBreakdownNotification'
      | 'dashboard'
      | 'dashboardCoachingName'
      | 'dashboardCoachingUtilities'
      | 'dashboardComparison'
      | 'dashboardCustom'
      | 'dashboardCustomLive'
      | 'dashboardCustomLiveGuideName'
      | 'dashboardCustomLiveName'
      | 'dashboardCustomLivePopup'
      | 'dashboardCustomLivePopupUtilities'
      | 'dashboardCustomLivePromptText'
      | 'dashboardCustomLiveRefreshRate'
      | 'dashboardCustomLiveScreenUnits'
      | 'dashboardCustomLiveUtilities'
      | 'dashboardCoaching'
      | 'dashboardCustomName'
      | 'dashboardCustomUtilities'
      | 'dashboardIndustry'
      | 'dashboardLive'
      | 'dashboardLiveGeofenceLimitCount'
      | 'dashboardLiveGuideName'
      | 'dashboardLiveRefreshRate'
      | 'dashboardLiveStatic'
      | 'dashboardLiveUtilities'
      | 'dashboardLiveWidgetsLimitCount'
      | 'dashboardMaintenance'
      | 'dashboardMaintenanceDate'
      | 'dashboardMapSettings'
      | 'dashboardMenuShortcut'
      | 'dashboardModeStatic'
      | 'dashboardOverview'
      | 'dashboardOverviewGuideName'
      | 'dashboardTopics'
      | 'dashboardUtilites'
      | 'dashboardVehicleDisplayName'
      | 'dataMbCredit'
      | 'dataUsageBuyMore'
      | 'datetimeFormat'
      | 'dateTimeSettings'
      | 'daysUntilLostVisibility'
      | 'defaultCountry'
      | 'defaultDateTimeLocale'
      | 'defaultDriversTableColumns'
      | 'defaultissuingcountry'
      | 'defaultMapLat'
      | 'defaultMapLon'
      | 'defaultMapZoom'
      | 'defaultTimezone'
      | 'defaultTimeZone'
      | 'defaultVehiclesTableColumns'
      | 'delivery'
      | 'deliveryAllowAdminManagementAccess'
      | 'deliveryAppointments'
      | 'deliveryAllowJobDispatchAccess'
      | 'deliveryAllowAppointmentAccess'
      | 'deliveryAppointmentsDismissNotificationsOnClick'
      | 'deliveryGuideUrl'
      | 'deliveryGuideUrlDefaultEN'
      | 'deliveryGuideUrlDefaultPL'
      | 'deliveryGuideUrlDefaultPT'
      | 'deliveryRouteView'
      | 'diagnostics'
      | 'diagnosticsRefreshRate'
      | 'didTagFaceId'
      | 'disableThirdPartyLogging'
      | 'distanceInMiles'
      | 'DOTNumber'
      | 'downloadGoogleTrip'
      | 'driverCustomDataEntity'
      | 'driverDuty'
      | 'driverIdentificationSettingsTab'
      | 'driversAddDriver'
      | 'driversAddGroup'
      | 'driversDeactivateDriver'
      | 'driversEditDriver'
      | 'driversImportDrivers'
      | 'driversMandatoryLicense'
      | 'drivingSideMode'
      | 'email'
      | 'enableClientFeedback'
      | 'enableReportPreviewUploading'
      | 'enableVehicleGroupSiteLocationsMapping'
      | 'enableWelcomePage'
      | 'engine'
      | 'facilityModuleNameType'
      | 'fieldService'
      | 'fuelInUsGallon'
      | 'genderLabel'
      | 'geofencesAddGeofence'
      | 'geofencesAddGroup'
      | 'geofenceScaleThreshold'
      | 'geofencesDeleteGeofence'
      | 'geofencesImportGeofences'
      | 'gmapApiKey'
      | 'googleLocationSearch'
      | 'googlePlayLink'
      | 'GPSFormat'
      | 'gpsMapIndicator'
      | 'hasNewPlatformAnnouncementBeenSeen'
      | 'hasVisionPromotionalModalBeenSeen'
      | 'help'
      | 'helpMobile'
      | 'helpMobileUrl'
      | 'helpWindowTool'
      | 'hereApiCode'
      | 'hereApiId'
      | 'hideVehicleRecentActivity'
      | 'impersonateUser'
      | 'isAdmin'
      | 'isRequiredMapSelectionAtSiteLocationsPage'
      | 'isSubUser'
      | 'knowTheDriver'
      | 'knowTheDriverFinance'
      | 'knowTheDriverFraud'
      | 'knowTheDriverProductivity'
      | 'knowTheDriverSafety'
      | 'knowTheDriverSecurity'
      | 'landmarksAddPOI'
      | 'landmarksDeleteLandmark'
      | 'landmarksEditMultiple'
      | 'landmarksImportPOI'
      | 'learningCenterMainAssetPath'
      | 'leftLatitude'
      | 'limitReportList'
      | 'limitsTab'
      | 'lineContacts'
      | 'list'
      | 'listAssets'
      | 'listAssetTrackers'
      | 'listBoard'
      | 'listDriverPassport'
      | 'listDrivers'
      | 'listDvirs'
      | 'listFacilities'
      | 'listGeofences'
      | 'listGroups'
      | 'listLandmarks'
      | 'listMaintenance'
      | 'maintenanceStatus'
      | 'maintenanceVehicleInspections'
      | 'listPoi'
      | 'listRoutes'
      | 'listTrailers'
      | 'listVehicles'
      | 'liveReportUrl'
      | 'localProxy'
      | 'locationSearch'
      | 'loginLanguageJson'
      | 'loginLanguageList'
      | 'loginMaxAttempts'
      | 'map'
      | 'mapApiProvider'
      | 'mapApiProviderAuth'
      | 'mapClustersEnabled'
      | 'mapCompareVehicles'
      | 'mapDefault'
      | 'mapEnableTripDownload'
      | 'mapFollowVehicle'
      | 'mapHeatMaps'
      | 'mapTags'
      | 'mapPollTime'
      | 'mapTimelineDateRange'
      | 'mapVehiclesPositionsRefreshRate'
      | 'mapVehiclesRefreshRate'
      | 'masterserver'
      | 'maxAllActivityDays'
      | 'maxDailyActivityDays'
      | 'maxGeofences'
      | 'maxTripLookupDays'
      | 'maxTripLookupMonths'
      | 'messageCreditsEditLimits'
      | 'messagesRefreshRate'
      | 'messaging'
      | 'minimumMapZoom'
      | 'maximumMapZoom'
      | 'mifleet'
      | 'mifleetImportAccidents'
      | 'mifleetImportDataV2'
      | 'mifleetImportFines'
      | 'mifleetImportFuelling'
      | 'mifleetImportFuelValidation'
      | 'mifleetImportMaintenance'
      | 'mifleetImportsGuideName'
      | 'mifleetImportSuppliers'
      | 'mifleetImportTolls'
      | 'mifleetLanguageId'
      | 'mifleetLanguagesOptions'
      | 'mifleetName'
      | 'miles_unit'
      | 'mobileStoreLinks'
      | 'npsPopupRepeatInSeconds'
      | 'npsScorePopupEnable'
      | 'npsScoreRepeatInSeconds'
      | 'passwordMaxAge'
      | 'phone'
      | 'phonePrefix'
      | 'primaryEmail'
      | 'privacy'
      | 'privacyHideLocationsFromDay'
      | 'privacyHideVehiclesLocationDataFromToday'
      | 'privacyModule'
      | 'alertsPushNotifications'
      | 'quickDialLicensesNumber'
      | 'remindersNotifications'
      | 'reportDataDurationLimitInMonths'
      | 'reportRepeatIntervalLimitInMonths'
      | 'reports'
      | 'reportsCustomize'
      | 'reportserver'
      | 'reportsFavorites'
      | 'reportsInformation'
      | 'reportsProfiles'
      | 'reportsSetup'
      | 'reportsSetupDownloadReport'
      | 'requestIndustryPopupIntervalInSeconds'
      | 'roadDescriptionType'
      | 'roadSpeed'
      | 'roi'
      | 'routes'
      | 'ruc'
      | 'rucPurchaseList'
      | 'rucLicenseAlmostExpiredInKm'
      | 'rucorReportId'
      | 'serverId'
      | 'sessionTimeoutMinutes'
      | 'showAverageFuelConsumption'
      | 'showCarpoolStats'
      | 'showingOverspeedThreshold'
      | 'showMapLivePositionButton'
      | 'showRoadDescriptionTypeSetting'
      | 'showTelegram'
      | 'showTripsFuelConsumptionMetaData'
      | 'showVehicleOverspeedThreshold'
      | 'signalRHubConnectionUrl'
      | 'signUpRepEmail'
      | 'sisenseApi'
      | 'sisenseDashboard'
      | 'sisenseDashboardCustomLiveVersion'
      | 'sisenseDashboardLiveVersion'
      | 'sisenseDashboardVersion'
      | 'sisenseJsUrl'
      | 'smsNotification'
      | 'smsShowDisclaimer'
      | 'specialInstructionsUtil'
      | 'specialLicensesLabel'
      | 'speedMph'
      | 'ssoHash'
      | 'state'
      | 'stylePositionUnreliableType'
      | 'styleProperties'
      | 'subFeatures'
      | 'tachograph'
      | 'tachographActivity'
      | 'tachographStatus'
      | 'tachographDownloads'
      | 'tachographDrivingTimes'
      | 'tachographRestPeriods'
      | 'tachographAttestationActivities'
      | 'tachographMapDrivers'
      | 'tachographWindowsAppUrl'
      | 'tachographWindowsAppFileSync'
      | 'telegramContacts'
      | 'temperatureInFahrenheit'
      | 'timeFormat24h'
      | 'timelineEconomyKmPerLiter'
      | 'timelineHasEconomyDisplay'
      | 'timelineHasGeofencesPois'
      | 'timelines'
      | 'timeZone'
      | 'timezoneForceServerTime'
      | 'timezoneForceServerTimeSettings'
      | 'timezoneOffset'
      | 'timezoneSettings'
      | 'trailersAddTrailer'
      | 'trailersDeleteTrailer'
      | 'trailersEditTrailer'
      | 'twoFactorAuthentication'
      | 'updateVehicleOdometer'
      | 'updateVehicleOdometerSubuser'
      | 'updateVehicleUnitClock'
      | 'useFederationLoginOnly'
      | 'useLocalTime'
      | 'userAudit'
      | 'userGuideFilename'
      | 'userImage'
      | 'userImportData'
      | 'userManageRoles'
      | 'userManageUsers'
      | 'userProfileSettings'
      | 'userProfileSettingsEditUser'
      | 'userSettings'
      | 'useWebSocketForLivePositions'
      | 'vehicle_enable_max_speed'
      | 'vehicleBasicInfo'
      | 'vehicleCustomDataEntity'
      | 'vehicleDetailsShowTerminalSerial'
      | 'vehicleGroupLabel'
      | 'vehicleInspectionActionApprove'
      | 'vehicleOpenVehicle'
      | 'vehiclePopoverList'
      | 'vehiclesAddGroup'
      | 'vehiclesDeactivateVehicle'
      | 'vehiclesEditVehicle'
      | 'vehiclesImportVehiclesToGroup'
      | 'vehiclesShareLocation'
      | 'vehiclesViewStatus'
      | 'vision'
      | 'visionBuyData'
      | 'visionDataManagement'
      | 'visionDataManagementLimitPct'
      | 'visionDiscoverMoreMailto'
      | 'visionDiscoverMoreMethod'
      | 'visionDiscoverMoreUrl'
      | 'visionEvents'
      | 'visionLiveStream'
      | 'visionManageLimits'
      | 'visionMaxVideoStreams'
      | 'visionSettings'
      | 'visionShowPreviewButton'
      | 'visionWatchVideoOnDemand'
      | 'visionWatchVideoStream'
      | 'enableVisionLanding'
      | 'zipCode'
      | 'carpoolAddVehicleCategoriesEnabled'
      | 'carpoolEditVehicleCategoriesEnabled'
      | 'carpoolDeleteVehicleCategoriesEnabled'
      | 'enableExperimentalVisionFootageRetrieval'
      | 'enableFridgeSensor'
      | 'enableFuelSensor'
      | 'enableOtherSensors'
      | 'enableImportReminders'
      | 'carpoolEditBookings'
      | 'carpoolCreateBookings'
      | 'carwatch'
      | 'llmChatUrl'
      | 'llmChat'
      | 'scorecards',
      // We use unknown ON PURPOSE. This forces devs to make a selector for each setting that parses it correctly. We should never assume a certain type for a setting.
      // We always need to parse it.
      unknown
    >
  >
}

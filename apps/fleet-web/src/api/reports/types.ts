/* eslint-disable @typescript-eslint/no-unused-vars */
import { arrayOfAllUnionTypes } from '@karoo/utils'
import { DateTime } from 'luxon'
import type { LiteralUnion } from 'type-fest'
import { z } from 'zod/v4'

import {
  geofenceGroupIdSchema,
  geofenceIdSchema,
  vehicleIdSchema,
  type DriverGroupIdWithGPrefix,
  type DriverId,
  type ReportId,
  type VehicleGroupIdWithGPrefix,
  type VehicleId,
} from 'api/types'
import type {
  parseCustomizedReportConfiguration,
  parseCustomReports,
  parseFavoriteAndCustomizedReports,
} from 'src/api/reports'
import type { ReportApiRepeatInterval } from 'src/modules/reports/ExportReport/types'
import { messages } from 'src/shared/forms/messages'
import type { BEBooleanShort, FixMeAny } from 'src/types'
import type { ExcludeStrict, ExtractStrict } from 'src/types/utils'
import { ctIntl } from 'src/util-components/ctIntl'

import type { CUSTOM_REPORT_FILTERS } from './util'

export type ReportPromptRawValidation = {
  type: LiteralUnion<'days' | 'ref' | 'date', string>
  min?: number
  ref?: LiteralUnion<'start_date' | 'pi_start_date', string>
  range?: string
  max?: string
}

export type ReportPromptWithoutValue = {
  identifier: PromptKnownFieldName
  name: string
  type: PromptKnownFieldType
  validation?: Array<ReportPromptRawValidation>
}

/****************************************************** Fetch Scheduled Report ********************************************************/

export type ReportDeliveryType = 'Email' | 'Web Link'

export const ScheduleReportParametersSchema = z.object({
  vehicle_limit: z.string().optional(),
  end_date: z.string().optional(),
  schedule: z.string().optional(),
  start_date: z.string().optional(),
  driver_name: z.string().optional(),
  working_hours_end: z.string().optional(),
  working_hours_start: z.string().optional(),
  pi_vehicles_list: z.string().optional(),
  pi_company_id: z.string().optional(),
  pi_end_date: z.string().optional(),
  pi_start_date: z.string().optional(),
})

export declare namespace ScheduleReportApi {
  type ParametersType = z.infer<typeof ScheduleReportParametersSchema> &
    Record<string, any>

  type ParametersKeyType = keyof ParametersType

  type ApiOutput = Array<{
    report_id: string
    repeat_interval: string
    execute_timestamp: string
    generate_timestamp: string
    report_format: string
    // NOTE: not the real report name, it's the path, one example value can be: "//fleet/summary_trip_report_v3.2.rpt",
    report_name: string
    delivery_to: string
    delivery_name: string
    report_state: string | `${number}`
    prompts: string
    parameters: ParametersType
  }>
}

// NOTE: this is used to pass the vehicle values with the type, it should be
/*
   {
    type: 'VEHLISTDETAIL'
    vehicle_id: 'all' | Array<VehicleId>
    vehicle_group_id: Array<VehicleGroupId>
   }
   or
   {
    type: 'VEHLISTDETAIL'
    vehicle_id: 'VehicleId, VehicleId2, ... VehicleIdN' // the old value can be string
    vehicle_group_id: Array<VehicleGroupId>
   }
   or
   {
    type: 'VEHLIS' | 'VEHLIST_NOALL' | 'VEHLIST_NOGROUPS_NOALL' | 'VEHLIST'
    vehicle_id: VehicleId
   }
   */
type VehiclePromptObjectValue = {
  identifier: PromptKnownVehicleTypeFieldName
  name: string
  type: ExtractStrict<PromptKnownVehicleFieldType, 'VEHLISTDETAIL'>
  value:
    | string
    | number // A VehicleId can be returned as a number sometimes
    | {
        vehicle_id: unknown // value can be 'all' or array of vehicle ids
        vehicle_group_id: unknown // Should be an array
      }
    | null
}

type DriverPromptObjectValue = {
  identifier: PromptKnownDriverTypeFieldName
  name: string
  type: PromptKnownDriverFieldType
  value:
    | {
        client_driver_id: unknown // Should be 'all' or array of driver ids
        client_group_id: unknown // Should be an array
      }
    | string
    | number
    | null
}

type GeneralScheduledReportPromptWithValue = {
  identifier: ExcludeStrict<PromptKnownFieldName, PromptKnownNumOrNumberTypeFieldName>
  name: string
  type: ExcludeStrict<
    PromptKnownFieldType,
    'VEHLISTDETAIL' | PromptKnownNumberFieldType | PromptKnownDriverFieldType
  >
  value: string | null | Record<string, unknown>
  validation?: Array<ReportPromptRawValidation>
}

type NumberPromptWithValue = {
  identifier: PromptKnownNumOrNumberTypeFieldName
  name: string
  type: PromptKnownNumberFieldType
  value: string | number | null
  validation?: Array<ReportPromptRawValidation>
}

export type ReportPromptsWithValue = Array<ReportPromptWithValue>

export type ReportPromptWithValue =
  | GeneralScheduledReportPromptWithValue
  | NumberPromptWithValue
  | VehiclePromptObjectValue
  | DriverPromptObjectValue

type ScheduledReportV2 = {
  // NOTE: the id here is not the same with the original report template id
  // which means we cannot get the original report data
  report_id: string | null
  report_name: string | null
  repeat_interval: string | null
  is_mifleet: boolean
  delivery_to: string | null // the email addresses
  execute_timestamp: string | null
  generate_timestamp: string | null
  report_format: unknown
  file_formats: unknown
  password: string | null
  data_duration?: string | null
  // NOTE: prompts are Array with value
  prompts: ReportPromptsWithValue | null
}

const ScheduleReportCompParametersSchema = z.object({
  vehicle: z.string().optional(),
  endDate: z.date().optional(),
  startDate: z.date().optional(),
  schedule: z.string().optional(),
  driver: z.string().optional(),
  startHour: z.string().optional(),
  endHour: z.string().optional(),
  miFleetVehicles: z.array(vehicleIdSchema).optional(),
  miFleetStartDate: z.date().optional(),
  miFleetEndDate: z.date().optional(),
})

export type ScheduleReportCompParametersType = z.infer<
  typeof ScheduleReportCompParametersSchema
>

export declare namespace FetchReportMiFleetResourcesApi {
  type MiFleetDriver = {
    cell_number: number
    driver_id: string
    email: string
    short_name: string | number // BE sometimes returns number if the name only contains numbers
  }

  type MiFleetVehicle = {
    plate: string
    vehicle_name: string | null
    vehicle_id: number | `${number}`
  }

  type ApiOutput = {
    objectList: Array<{
      drivers: Array<MiFleetDriver>
      vehicles: Array<MiFleetVehicle>
    }>
    totalCount: number
    success: boolean
    action: string
  }
}

export declare namespace FetchScheduledReportsV2Api {
  type ApiOutput = {
    ct_fleet_get_schedule_reports_v2: Array<
      {
        report_state: string | `${number}`
        prompt_fields?: string
        date_range_in_days?: string
      } & ScheduledReportV2
    >
  }
}

/****************************************************** Fetch Report Status ********************************************************/

export declare namespace GetReportStatusV2Api {
  type ApiOutput = {
    ct_fleet_get_report_status_v2: Array<
      ScheduledReportV2 & {
        type_string: unknown
        state_string: string | null
        translation_id: string | null
      }
    >
  }
}

export declare namespace UpdateScheduledReportsV2Api {
  type ApiInput = {
    report_id: ReportId
    file_format: string
    delivery_type: string
    send_date?: string
    password?: string
    prompts: Array<ReportPromptValueTypeApiInput>
    emails?: string
    schedule?: boolean
    data_duration?: string
    repeat_interval?: string
    timezone?: number
  }
}

export declare namespace CancelScheduledReportsApi {
  type ApiOutput = {
    ct_fleet_get_schedule_reports: ScheduleReportApi.ApiOutput
  }
}

type FavoriteReport = {
  report_description: string | null
  report_id: string
  report_name: string
  report_type: string
}

export type CustomizedReport = {
  report_id: string
  report_name: string
  report_type: string
  report_user_customized_id: string
}

export declare namespace FetchFavoriteAndCustomizedReportsApi {
  type ApiOutput = {
    ct_fleet_get_customized_reports: Array<CustomizedReport>
    ct_fleet_get_favourite_reports: Array<FavoriteReport>
  }
}

export declare namespace DeleteSavedCustomizedReportsApi {
  type ApiOutput = {
    ct_fleet_get_customized_reports: Array<CustomizedReport>
  }
}

export declare namespace FetchFavoriteReportsApi {
  type ApiOutput = {
    ct_fleet_get_favourite_reports: Array<FavoriteReport>
  }
}

export declare namespace FetchReportPreviewApi {
  type ApiOutput = Response
}

type FavoriteReportObjType = ReturnType<
  typeof parseFavoriteAndCustomizedReports
>['favoriteReports']

type FavoriteReportObjKeyType = keyof FavoriteReportObjType

export type FavoriteReportType = FavoriteReportObjType[FavoriteReportObjKeyType]

export type SavedCustomizedReportsType = ReturnType<
  typeof parseFavoriteAndCustomizedReports
>['customizedReports']

/****************************************************** Fetch Custom Report Resources ********************************************************/

export declare namespace FetchCustomReportResourcesApi {
  type ApiOutput = {
    ct_fleet_get_client_driver_list: Array<{
      client_driver_id: string
      client_user_id: string | null
      driver_name: string
    }>
    ct_fleet_get_client_vehicle_list: Array<{
      client_vehicle_description: string | null
      client_vehicle_description1: string | null
      client_vehicle_description2: string | null
      registration: string
      vehicle_id: string
    }>
    ct_fleet_get_client_geofence_list_light: Array<{
      geofence_id: string
      geofence_name: string
    }>
    ct_fleet_get_client_geofence_list_groups: Array<{
      group_geofence_id: string
      name: string
    }>
  }
}

/****************************************************** Fetch Custom Report ********************************************************/

export type CustomReportFilterKeyType = keyof typeof CUSTOM_REPORT_FILTERS

export type CustomReportCustomFieldFormType = {
  key: string
  label: string
  type: CustomReportFilterKeyType
}

export type CustomReportCustomFieldArrayFormType =
  Array<CustomReportCustomFieldFormType>

export type SelectedCustomField = CustomReportCustomFieldFormType['key']

export type CustomReportFilterOperatorValueType =
  | (typeof CUSTOM_REPORT_FILTERS)[ExcludeStrict<
      CustomReportFilterKeyType,
      'VEHLIST'
    >][number]['value']
  | 'None'
  | null

export type CustomizedReportFormFilterWithoutType = {
  operator: CustomReportFilterOperatorValueType
  value: string
}

export type CustomizedReportFormFilterType = CustomizedReportFormFilterWithoutType & {
  type: CustomReportFilterKeyType
}

export type CustomizedReportFormFiltersObjWithoutType = Record<
  string,
  Array<CustomizedReportFormFilterWithoutType>
>

export type CustomizedReportFormFiltersObjWithType = Record<
  string,
  Array<CustomizedReportFormFilterType>
>

export type CustomizedReportConfigurationFilterTypeForApi = Pick<
  CustomReportCustomFieldFormType,
  'key'
> &
  CustomizedReportFormFilterWithoutType

export declare namespace FetchCustomReportsApi {
  type RawReportTypeType = { value: 'pdf' | 'xls' | 'csv'; label: string }
  type CustomReportType = {
    report_id: string
    report_name: string
    delivery_types: Array<string>
    prompts: Array<ReportPromptWithoutValue>
    report_fields: string
    report_description: string | null
    zip_attachment: ServerBoolType
    priority_level: `${number}` | null
    category: string
    typesArray: Array<RawReportTypeType>
    report_file: string
    date_range_in_days: `${number}` | null
  }
  type ApiOutput = {
    ct_fleet_get_custom_reports: Array<CustomReportType>
  }
}

export type CustomReportsType = ReturnType<typeof parseCustomReports>

/****************************************************** Export Report ********************************************************/

// NOTE: this new api type uses the same api as the old one, it just be more flexible

export declare namespace ExportReportNewApi {
  type ApiInput = {
    id: string
    file_format: string
    delivery_type: string
    prompts: Array<ReportPromptValueTypeApiInput>
    zipped?: boolean
    password?: string
    email_address?: string
    send_date?: string
    schedule?: boolean
    schedule_end?: string
    repeat_interval?: ReportApiRepeatInterval
    timezone?: number
  }
}

/****************************************************** Create Customized Report ********************************************************/

// FIXME: the extension value should be fixed strings
export type AvailableDownloadFileType = { extension: string; description: string }

type ServerBoolType = 'f' | 't'

type CustomizedReportConfigurationForApi = {
  id: string
  report_user_customized_id?: string
  name: string

  // from selected report, actually file path, to get name displayed in the report status
  reportname: string

  // from basic form
  save_custom_report: boolean
  email_address: string
  file_format: string
  delivery_type: ReportDeliveryType
  schedule: boolean
  repeat_interval: string
  schedule_end?: string
  password?: string
  prompts: string
  customFields: string
  suppressHeader: boolean
  suppressFooter: boolean
  execute_timestamp: string // this is the send date
  generatetimestamp: string

  // From settings:
  altdatasource: string
  timeZone: number | null

  // required
  deliverytype: number
  deliveryto: string

  // for report status and schedule api
  deliveryname: string
  reportformat: string
}

export declare namespace CreateCustomizedReportApi {
  type ApiInput = CustomizedReportConfigurationForApi & {
    filters: Array<CustomizedReportConfigurationFilterTypeForApi>
    suppressHeader: boolean
    suppressFooter: boolean
    zip_attachment: boolean

    retry_count: number
    default_retry: number
    default_retry_interval: number
  }
}

export declare namespace CreateNewCustomizedReportApi {
  type ApiInput = CustomizedReportConfigurationForApi & {
    filters: Array<CustomizedReportConfigurationFilterTypeForApi>
    suppressHeader: boolean
    suppressFooter: boolean
    zip_attachment: boolean

    retry_count: number
    default_retry: number
    default_retry_interval: number
  }
}

export declare namespace FetchCustomizedReportConfigurationApi {
  type ApiOutput = {
    report_id: string
    report_user_customized_id: string
    report_name: string
    report_data: CustomizedReportConfigurationForApi & {
      filters: unknown
      suppress_headers: ServerBoolType
      suppress_footers: ServerBoolType
      zip_attachment: ServerBoolType
    }
  }
}

export type CustomizedReportConfigurationType = ReturnType<
  typeof parseCustomizedReportConfiguration
>

export declare namespace GetReportOptionsV2Api {
  type ApiOutput = Array<{
    name: string
    report_id: string
    report_description: null | string
    category: null | LiteralUnion<'MiFleet', string>
    file_formats: Array<{
      extension: LiteralUnion<'NULL', string>
      description: string
    }>
    prompts: Array<ReportPromptWithoutValue> | null
    allow_zip: BEBooleanShort
    delivery_types: Array<LiteralUnion<ReportDeliveryType, string>> | null
  }>
}

export declare namespace Ct_fleet_delete_favourite_report {
  type ApiOutput = FetchFavoriteAndCustomizedReportsApi.ApiOutput
}

/****************************************************** Report Prompt Fields ********************************************************/

/**
 * Example 1 prompt from sg
 * {
    "Time_End=Position Time End=TIME": 1,
    "Time_Start=Position Time Start=TIME": 1,
    "client_driver_id=Driver=DRIVERLIST": 17,
    "client_user_id=user_id=USER_ID": 257,
    "driver_limit=Driver=DRIVERLIST": 20,
    "driver_name=Driver=DRIVERLIST": 3,
    "end_date=End Date=DATE-E": 164,
    "end_date=End Date=DATETIME": 3,
    "end_date=End Date=DATETIME-E": 78,
    "end_date=End date=DATE-E": 1,
    "end_time=End Date=DATE-E": 1,
    "geofence=Geofence=GEOFENCE": 1,
    "geofence_group=Geofence Group=GEOFENCEGROUP": 11,
    "geofence_id=Geofence=GEOFENCE": 4,
    "in_interval=Interval in Minutes=NUMBER": 2,
    "min_visits=Min Visits=NUMBER": 1,
    "pi_company_id=USER_ID": 40,
    "pi_document_concepts": 1,
    "pi_driver_list=Driver Name=DRIVERLIST": 5,
    "pi_end_date=End Date=DATE-E": 38,
    "pi_start_date=Start Date=DATE-S": 38,
    "pi_vehicles_list=Registration=VEHLIST": 31,
    "pi_vehicles_list=Registration=VEHLISTDETAIL": 1,
    "pi_vehicles_list=Registration=VEHLIST_NOGROUPS_NOALL": 1,
    "rest_days=Rest Days (type in the days, separated by a comma)=FREE_TEXT": 1,
    "speed_limiter=Above [km\/h]=NUMBER": 2,
    "start_date=Date=DATE-S": 3,
    "start_date=Start Date=DATE": 1,
    "start_date=Start Date=DATE-S": 165,
    "start_date=Start Date=DATETIME": 81,
    "start_date=Starting date = DATE-S": 1,
    "start_time=Start Date=DATE-S": 1,
    "start_time=Time=TIME": 3,
    "user_id=user_id=USER_ID": 1,
    "user_name=user=USER_NAME": 83,
    "vehicle_limit=Registration No=VEHLIST": 1,
    "vehicle_limit=Registration=VEHLIS": 1,
    "vehicle_limit=Registration=VEHLIST": 233,
    "working_hours_end=End of Work Hours=TIME": 52,
    "working_hours_start=Start of Work Hours=TIME": 52
}

Example 2 prompt from pt
{
    "AvgFuel=M\u00e9dia Consumo (x.xx)=DESC": 2,
    "FuelCost=Custo Combustivel (x.xx)=DESC": 2,
    "GEOLIST=GEOLIST=DESC": 1,
    "Title1=Campanha=DESC": 2,
    "Title1=test desc=DESC": 1,
    "Title3=Folhetos=DESC": 2,
    "Title3=test num=NUM": 1,
    "accel=Acelera\u00e7\u00f5es (0.15,0.3,0.4,1,3,9)=DESC": 1,
    "braking=Travagens (0.15,0.3,0.4,1,3,9)=DESC": 1,
    "client_driver_id=Driver=DRIVERLIST": 1,
    "client_name=driver=DRIVERLIST": 1,
    "client_user_id=user_id=USER_ID": 928,
    "corner=Viragens (0.2,0.4,0.5,1,3,9)=DESC": 1,
    "date=Dia=DATE-S": 1,
    "day=Dia=DATE-S": 7,
    "driver_id=Condutor=DRIVERLIST": 4,
    "driver_id=Motorista=DRIVERLIST": 2,
    "driver_limit=Matricula=DRIVERLIST": 1,
    "driver_name=Condutor=DRIVERLIST": 16,
    "driver_name=driver=DRIVERLIST": 56,
    "end_date=Data Fim=DATE-E": 823,
    "end_date=Data Fim=DATETIME": 9,
    "end_date=Data Fim=DATETIME-E": 48,
    "end_date=Data ko\u0144cowa=DATE-E": 6,
    "end_date=End Date=DATE-E": 5,
    "end_date=End Date=DATETIME-E": 3,
    "end_date=Fecha Fin=DATE-E": 3,
    "end_geofence=F\u00e1brica=GEOFENCE": 3,
    "end_geofence=Geofence Fim=GEOFENCE": 2,
    "end_time=Horas de trabalho Fim=TIME": 1,
    "end_time_afternoon=Hora Fim Tarde=TIME": 1,
    "end_time_morning=Hora Fim Manh\u00e3=TIME": 1,
    "end_timestamp=Data Fim=DATE-E": 1,
    "end_ts=Data Fim=DATE-E": 1,
    "geofence=Geofence=GEOFENCE": 1,
    "geofence_group=Geofence Group=GEOFENCEGROUP": 25,
    "geofence_id=Geofence=GEOFENCE": 19,
    "group_geofence=Geofence Group=GEOFENCEGROUP": 1,
    "group_id=Grupo=VEHLIST": 2,
    "group_id=grupo=DRIVERLIST": 2,
    "group_id=grupo=VEHLIST": 2,
    "grouped=Agrupar -> 1 (S) 0 (N) =DESC": 2,
    "km_cost=Custo Km (x,xx)=DESC": 2,
    "km_cost=Custo Km (x.xx)=DESC": 8,
    "pi_company_id=USER_ID": 38,
    "pi_document_concepts": 1,
    "pi_driver_list=Driver Name=DRIVERLIST": 5,
    "pi_end_date=End Date=DATE-E": 36,
    "pi_start_date=Start Date=DATE-S": 36,
    "pi_vehicles_list=Registration=VEHLIST": 29,
    "pi_vehicles_list=Registration=VEHLISTDETAIL": 1,
    "pi_vehicles_list=Registration=VEHLIST_NOGROUPS_NOALL": 1,
    "profile_id=Report Profile=PROFILE": 1,
    "pto_distance=N. metros PTO=DESC": 1,
    "reg=Matr\u00edcula (aa-bb-cc)=DESC": 6,
    "start_date=Data Inicio=DATE-E": 14,
    "start_date=Data Inicio=DATE-S": 12,
    "start_date=Data In\u00edcio=DATE": 4,
    "start_date=Data In\u00edcio=DATE-S": 793,
    "start_date=Data In\u00edcio=DATETIME": 57,
    "start_date=Data pocz\u0105tkowa=DATE-S": 6,
    "start_date=Data=DATE-S": 4,
    "start_date=Fecha Inicio=DATE-S": 3,
    "start_date=Start Date=DATE-S": 5,
    "start_date=Start Date=DATETIME": 3,
    "start_geofence=Geofence In\u00edcio=GEOFENCE": 2,
    "start_time=Horas de trabalho In\u00edcio=TIME": 1,
    "start_time_afternoon=Hora In\u00edcio Tarde=TIME": 1,
    "start_time_morning=Hora In\u00edcio Manh\u00e3=TIME": 1,
    "start_timestamp=Data In\u00edcio=DATE-S": 1,
    "start_ts=Data In\u00edcio=DATE-S": 1,
    "temp_seg=Varia\u00e7\u00f5es de (mintuos)=NUM": 7,
    "time=Data=DATETIME": 1,
    "time_open=Tempo Abertura(min)=NUM": 1,
    "time_seconds=Dura\u00e7\u00e3o(segundos) superior a:=NUMBER": 3,
    "user_id=USER_ID": 2,
    "user_id=user=USER_ID": 1,
    "user_name=user=USER_NAME": 351,
    "vehicle_limit=Grupo=VEHLIST": 1,
    "vehicle_limit=Matricula=VEHLIST": 12,
    "vehicle_limit=Matr\u00edcula=VEHLIST": 845,
    "vehicle_limit=Nr rejestracyjny=VEHLIST": 6,
    "vehicle_limit=Registration=VEHLIST": 8,
    "vmin=Minimo=DESC": 1,
    "working_hours_end=Fim Horas de Trabalho (hh:mm)=DESC": 21,
    "working_hours_end=Fim Horas de Trabalho (hh:mm)=TIME": 11,
    "working_hours_end=Fim Horas de Trabalho (xx:xx)=TIME": 2,
    "working_hours_end=Fim Horas de Trabalho=TIME": 131,
    "working_hours_end=Fim Hor\u00e1rio Profissional=TIME": 8,
    "working_hours_end=Fin de horas de trabajo=TIME": 1,
    "working_hours_end=Godzina zako\u0144czenia pracy=TIME": 1,
    "working_hours_end=Horas de Fim=TIME": 1,
    "working_hours_end=Horas de trabalho Fim=TIME": 14,
    "working_hours_start=Comece a Trabalhar Horas (hh:mm) =DESC": 1,
    "working_hours_start=Comece a Trabalhar Horas (hh:mm)=DESC": 20,
    "working_hours_start=Comece a Trabalhar Horas (hh:mm)=TIME": 11,
    "working_hours_start=Comece a Trabalhar Horas (xx:xx)=TIME": 2,
    "working_hours_start=Comece a Trabalhar Horas=TIME": 131,
    "working_hours_start=Godzina rozpocz\u0119cia pracy=TIME": 1,
    "working_hours_start=Horas Inicio=TIME": 1,
    "working_hours_start=Horas de trabalho In\u00edcio=TIME": 14,
    "working_hours_start=Inicio de horas de trabajo=TIME": 1,
    "working_hours_start=In\u00edcio Hor\u00e1rio Profissional=TIME": 8,
    "year=Ano=NUM": 2
}

Example 3 prompt from ph
{
   "interval=interval=INTERVAL": 1,
}
 */

/****************************************************** Report Prompt Number Type ********************************************************/

const numTypeKnownPromptSchemaShape = {
  // Title3: z.string().optional(),
  time_open: z.string().optional(),
  temp_seg: z.string().optional(),
  year: z.string().optional(), // NOTE: it's kind of special
}

const intervalTypeKnownPromptSchemaShape = {
  interval: z.string().optional(),
}

const numberTypeKnownPromptSchemaShape = {
  in_interval: z.string().optional(),
  min_visits: z.string().optional(),
  speed_limiter: z.string().optional(),
  speed_limit: z.string().optional(),
  pi_price_km_pay: z.string().optional(),
  pi_price_km_recieve: z.string().optional(),
  time_seconds: z.string().optional(),
  trip_limit: z.string().optional(),
  swathe: z.string().optional(),
  min_duration: z.string().optional(),
  in_event_limit: z.string().optional(),
  in_top_vehicle_limit: z.string().optional(),
  speed: z.string().optional(),
  cost: z.string().optional(),
  Idlelimit: z.string().optional(),
  idle_minutes_min: z.string().optional(),
}

export const numOrNumberTypeKnownPromptSchemaShape = {
  ...intervalTypeKnownPromptSchemaShape,
  ...numTypeKnownPromptSchemaShape,
  ...numberTypeKnownPromptSchemaShape,
}

const numOrNumberTypeKnownPromptSchema = z.object(numOrNumberTypeKnownPromptSchemaShape)

const intervalTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof intervalTypeKnownPromptSchemaShape
>()(['interval'])

const numberTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof numberTypeKnownPromptSchemaShape
>()([
  'in_interval',
  'min_visits',
  'speed_limiter',
  'speed_limit',
  'pi_price_km_pay',
  'pi_price_km_recieve',
  'time_seconds',
  'trip_limit',
  'swathe',
  'min_duration',
  'in_event_limit',
  'in_top_vehicle_limit',
  'speed',
  'cost',
  'Idlelimit',
  'idle_minutes_min',
])

const numTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof numTypeKnownPromptSchemaShape
>()(['time_open', 'temp_seg', 'year'])

export const numOrNumberTypeKnownPromptFieldNames = [
  ...numTypeKnownPromptFieldNames,
  ...numberTypeKnownPromptFieldNames,
  ...intervalTypeKnownPromptFieldNames,
] as const

export type PromptKnownNumOrNumberTypeFieldName = keyof z.infer<
  typeof numOrNumberTypeKnownPromptSchema
>

const dateTimeZodSchema = z
  .custom<DateTime>((value): boolean => DateTime.isDateTime(value))
  .nullable()

/****************************************************** Report Prompt Time Type ********************************************************/

export const endTimeTypeKnownPromptSchemaShape = {
  working_hours_end: dateTimeZodSchema,
  end_time: dateTimeZodSchema,
  end_time_afternoon: dateTimeZodSchema,
  end_time_morning: dateTimeZodSchema,
  Time_End: dateTimeZodSchema,
  work_end: dateTimeZodSchema,
  abnormal_end: dateTimeZodSchema,
  end_work_hours: dateTimeZodSchema,
}

export const startTimeTypeKnownPromptSchemaShape = {
  working_hours_start: dateTimeZodSchema,
  start_time: dateTimeZodSchema,
  hours_start: dateTimeZodSchema,
  start_time_afternoon: dateTimeZodSchema,
  start_time_morning: dateTimeZodSchema,
  Time_Start: dateTimeZodSchema,
  work_start: dateTimeZodSchema,
  abnormal_start: dateTimeZodSchema,
  start_work_hours: dateTimeZodSchema,
}

export const timeTypeKnownPromptSchemaShape = {
  ...startTimeTypeKnownPromptSchemaShape,
  ...endTimeTypeKnownPromptSchemaShape,
}

const timeTypeKnownPromptSchema = z.object(timeTypeKnownPromptSchemaShape)

export const endTimeTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof endTimeTypeKnownPromptSchemaShape
>()([
  'working_hours_end', // these two can be type of DESC
  'end_time_afternoon',
  'end_time_morning',
  'Time_End',
  // DATE or TIME, treat as date range when both exist and types are DATE
  // otherwise just time
  'end_time',
  'abnormal_end',
  'work_end',
  'end_work_hours',
])

export const startTimeTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof startTimeTypeKnownPromptSchemaShape
>()([
  'working_hours_start',
  'hours_start',
  'start_time_afternoon',
  'start_time_morning',
  'Time_Start',
  // DATE or TIME, treat as date range when both exist and types are DATE
  // otherwise just time
  'start_time',
  'abnormal_start',
  'work_start',
  'start_work_hours',
])

export type PromptKnownTimeTypeFieldName = keyof z.infer<
  typeof timeTypeKnownPromptSchema
>

export const timeTypeKnownPromptFieldNames = [
  ...startTimeTypeKnownPromptFieldNames,
  ...endTimeTypeKnownPromptFieldNames,
] as const

/****************************************************** Report Prompt String Type ********************************************************/

export const freeTextTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof freeTextTypeKnownPromptSchemaShape
>()(['rest_days'])

export const descTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof descTypeKnownPromptSchemaShape
>()([
  // DESC
  'AvgFuel',
  'FuelCost',
  'Title1',
  'Title2',
  'Title3',
  'km_cost',
  'reg',
  'vmin',
  'accel',
  'braking',
  'corner',
  'grouped',
  'pto_distance',
  'minutes',
  'folgas',
])

export const usernameTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof usernameTypeKnownPromptSchemaShape
>()(['user_name', 'username'])

export const userIdTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof userIdTypeKnownPromptSchemaShape
>()(['client_user_id', 'pi_company_id', 'user_id'])

export const profileTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof profileIdTypeKnownPromptSchemaShape
>()(['profile_id'])

export const branchTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof branchTypeKnownPromptSchemaShape
>()(['branch_id'])

export const stringTypeKnownPromptFieldNames = [
  ...freeTextTypeKnownPromptFieldNames,
  ...descTypeKnownPromptFieldNames,
  ...usernameTypeKnownPromptFieldNames,
  ...userIdTypeKnownPromptFieldNames,
  ...profileTypeKnownPromptFieldNames,
  ...branchTypeKnownPromptFieldNames,
] as const

const descTypeKnownPromptSchemaShape = {
  AvgFuel: z.string().optional(),
  FuelCost: z.string().optional(),
  Title1: z.string().optional(),
  Title2: z.string().optional(),
  Title3: z.string().optional(),
  km_cost: z.string().optional(),
  reg: z.string().optional(),
  vmin: z.string().optional(),
  accel: z.string().optional(),
  braking: z.string().optional(),
  corner: z.string().optional(),
  grouped: z.string().optional(),
  pto_distance: z.string().optional(),
  minutes: z.string().optional(),
  folgas: z.string().optional(),
}

const usernameTypeKnownPromptSchemaShape = {
  username: z.string().optional(),
  user_name: z.string().optional(),
}

const userIdTypeKnownPromptSchemaShape = {
  client_user_id: z.string().optional(),
  user_id: z.string().optional(),
  pi_company_id: z.string().optional(),
}

const branchTypeKnownPromptSchemaShape = {
  branch_id: z.string().optional(),
}

const freeTextTypeKnownPromptSchemaShape = {
  rest_days: z.string().optional(),
}

export const stringTypeKnownPromptSchemaShape = {
  ...descTypeKnownPromptSchemaShape,
  ...usernameTypeKnownPromptSchemaShape,
  ...userIdTypeKnownPromptSchemaShape,
  ...branchTypeKnownPromptSchemaShape,
  ...freeTextTypeKnownPromptSchemaShape,
}

const stringTypeKnownPromptSchema = z.object(stringTypeKnownPromptSchemaShape)

export type PromptKnownStringTypeFieldName = keyof z.infer<
  typeof stringTypeKnownPromptSchema
>
/****************************************************** Report Prompt Geofence ********************************************************/

const geofenceSchema = z
  .object({ value: geofenceIdSchema.or(z.literal('none')), label: z.string() })
  .nullable()

export const GeofenceReportSchema = z.object({
  geofence: geofenceSchema,
  geo_1: geofenceSchema,
  geo_2: geofenceSchema,
  geofence_id: geofenceSchema,
  start_geofence: geofenceSchema,
  end_geofence: geofenceSchema,
})

export type PromptKnownGeofenceTypeFieldName = keyof z.infer<
  typeof GeofenceReportSchema
>

export const PROMPT_KNOWN_GEOFENCE_TYPE_FIELD_NAME =
  arrayOfAllUnionTypes<PromptKnownGeofenceTypeFieldName>()([
    'geofence',
    'geo_1',
    'geo_2',
    'geofence_id',
    'start_geofence',
    'end_geofence',
  ])

/****************************************************** Report Prompt Geofence Group ********************************************************/

const getGeofenceGroupSchema = () =>
  z.object(
    {
      value: geofenceGroupIdSchema.or(z.literal('none')),
      label: z.string(),
    },
    {
      error: (issue) =>
        issue.code === 'invalid_type'
          ? ctIntl.formatMessage({ id: messages.required })
          : undefined,
    },
  )

export const getGeofenceGroupReportSchema = () =>
  z.object({
    geofence_group: getGeofenceGroupSchema(),
    geofence_group2: getGeofenceGroupSchema(),
    geofence_group3: getGeofenceGroupSchema(),
    geofence_group4: getGeofenceGroupSchema(),
    group_geofence: getGeofenceGroupSchema(),
  })

export type PromptKnownGeofenceGroupTypeFieldName = keyof z.infer<
  ReturnType<typeof getGeofenceGroupReportSchema>
>

export const PROMPT_KNOWN_GEOFENCE_GROUP_TYPE_FIELD_NAME =
  arrayOfAllUnionTypes<PromptKnownGeofenceGroupTypeFieldName>()([
    'geofence_group',
    'geofence_group2',
    'geofence_group3',
    'geofence_group4',
    'group_geofence',
  ])

/****************************************************** Report Prompt DayOfWeek Type ********************************************************/

export type PromptKnownDayOfWeekTypeFieldName = 'dayofweek' | 'in_dow'

export const PROMPT_KNOWN_DAY_OF_WEEK_TYPE_FIELD_NAME =
  arrayOfAllUnionTypes<PromptKnownDayOfWeekTypeFieldName>()(['dayofweek', 'in_dow'])

/****************************************************** Report Prompt Single Select Type ********************************************************/

export const singleSelectZodSchema = z.string().nullable()

export const updownTypeKnownPromptSchemaShape = {
  // LIST(UP/DOWN/NONE)
  updown: singleSelectZodSchema,
}

export const durationTypeKnownPromptSchemaShape = {
  // TIMEFINE
  duration: singleSelectZodSchema,
}

const profileIdTypeKnownPromptSchemaShape = {
  profile_id: singleSelectZodSchema,
}

export const updownTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof updownTypeKnownPromptSchemaShape
>()(['updown'])

export const durationTypeKnownPromptFieldNames = arrayOfAllUnionTypes<
  keyof typeof durationTypeKnownPromptSchemaShape
>()(['duration'])

export const singleSelectTypeKnownPromptSchemaShape = {
  ...updownTypeKnownPromptSchemaShape,
  ...durationTypeKnownPromptSchemaShape,
  ...profileIdTypeKnownPromptSchemaShape,
}

const singleSelectKnownPromptSchema = z.object(singleSelectTypeKnownPromptSchemaShape)

export type PromptKnownSingleSelectTypeFieldName = keyof z.infer<
  typeof singleSelectKnownPromptSchema
>

export const singleSelectTypeKnownPromptFieldNames = [
  ...updownTypeKnownPromptFieldNames,
  ...durationTypeKnownPromptFieldNames,
] as const

/****************************************************** Report Prompt Vehicle Type ********************************************************/

export const PROMPT_KNOWN_VEHICLE_TYPE_FIELD_NAME = [
  'vehicle_limit',
  'Vehicle Name',
  'group_id', // can be VEHICLE or DRIVER
  'pi_vehicles_list',
] as const

export type PromptKnownVehicleTypeFieldName =
  (typeof PROMPT_KNOWN_VEHICLE_TYPE_FIELD_NAME)[number]

export const PROMPT_KNOWN_DRIVER_TYPE_FIELD_NAME = [
  'driver_id',
  'driver_name',
  'driver_limit',
  'client_driver_id',
  'pi_driver_list',
  'group_id',
] as const

export type PromptKnownDriverTypeFieldName =
  (typeof PROMPT_KNOWN_DRIVER_TYPE_FIELD_NAME)[number]

export const PROMPT_KNOWN_DATE_OR_DATETIME_TYPE_FIELD_NAME = [
  'pi_end_date',
  'pi_start_date',
  'end_date',
  'start_date',
  'end_timestamp',
  'start_timestamp',
  'end_ts',
  'start_ts',
  'start_time', // NOTE: this is special, can be DATE or TIME
  'time', // DATETIME, as start datetime
  'date', // DATE TODO:
  'day', // DATE TODO:
] as const

export type PromptKnownDateOrDateTimeTypeFieldName =
  (typeof PROMPT_KNOWN_DATE_OR_DATETIME_TYPE_FIELD_NAME)[number]

export type PromptKnownFieldName =
  // DATE, DATETIME
  | PromptKnownDateOrDateTimeTypeFieldName

  // TIME
  | PromptKnownTimeTypeFieldName

  // VEHICLE
  | PromptKnownVehicleTypeFieldName

  // DRIVER
  | PromptKnownDriverTypeFieldName

  // GEOFENCE
  | PromptKnownGeofenceTypeFieldName

  // GEOFENCE_GROUP
  | PromptKnownGeofenceGroupTypeFieldName

  // NUMBER or NUM
  | PromptKnownNumOrNumberTypeFieldName

  // DESC, USER_NAME, USER_ID, PROFILE, BRANCH
  | PromptKnownStringTypeFieldName

  // DOW
  | PromptKnownDayOfWeekTypeFieldName

  // TIMEFINE, LIST(UP/DOWN/NONE)
  | PromptKnownSingleSelectTypeFieldName

  // UNKNOWN
  | 'pi_document_concepts'
  | 'rest_days'

export type PromptKnownStringFieldType =
  | 'DESC'
  | 'USER_ID'
  | 'USER_NAME'
  | 'string'
  | 'BRANCH'

export type PromptKnownNumberFieldType = 'NUM' | 'NUMBER' | 'INTERVAL'

export type PromptKnownTimeFieldType = 'TIME' | 'TIME-E' | 'TIME-S'

export type PromptKnownDayOfWeekFieldType = 'DOW'

export type PromptKnownSingleSelectFieldType =
  | 'LIST(UP/DOWN/NONE)'
  | 'TIMEFINE'
  | 'PROFILE'

export type PromptKnownGeofenceFieldType = 'GEOFENCE'

export type PromptKnownGeofenceGroupFieldType = 'GEOFENCEGROUP'

export const PROMPT_KNOWN_VEHICLE_FIELD_TYPE = [
  'VEHLIS',
  'VEHLIST_NOALL',
  'VEHLIST_NOGROUPS_NOALL',
  'VEHLIST',
  'VEHLISTDETAIL',
] as const

export type PromptKnownVehicleFieldType =
  (typeof PROMPT_KNOWN_VEHICLE_FIELD_TYPE)[number]

export const PROMPT_KNOWN_DRIVER_FIELD_TYPE = [
  'DRIVERLIST_NOALL',
  'DRIVERLIST',
] as const

export type PromptKnownDriverFieldType = (typeof PROMPT_KNOWN_DRIVER_FIELD_TYPE)[number]

export const PROMPT_KNOWN_DATE_RANGE_FIELD_TYPE = [
  'DATE-E',
  'Date-S',
  'DATE-S',
  'DATE',
  'DATETIME-E',
  'DATETIME-S',
  'DATETIME-5MIN',
  'DATETIME',
] as const

export type PromptKnownDateRangeFieldType =
  (typeof PROMPT_KNOWN_DATE_RANGE_FIELD_TYPE)[number]

export type PromptKnownFieldType =
  | ''
  | 'FREE_TEXT'
  | 'POI'
  // Questionable but apparently present in the API
  | 'ID'
  | PromptKnownDateRangeFieldType
  | PromptKnownStringFieldType
  | PromptKnownNumberFieldType
  | PromptKnownVehicleFieldType
  | PromptKnownDriverFieldType
  | PromptKnownTimeFieldType
  | PromptKnownSingleSelectFieldType
  | PromptKnownGeofenceFieldType
  | PromptKnownGeofenceGroupFieldType
  | PromptKnownDayOfWeekFieldType

/****************************************************** Report Prompt Value Type ********************************************************/

export type RegistrationValuePromptTypeApiInput =
  | {
      identifier: PromptKnownVehicleTypeFieldName
      value: 'all' | VehicleId | VehicleGroupIdWithGPrefix
      type: ExcludeStrict<PromptKnownVehicleFieldType, 'VEHLISTDETAIL'>
    }
  | {
      identifier: PromptKnownVehicleTypeFieldName
      type: 'VEHLISTDETAIL'
      value:
        | [{ vehicle_id: 'all' }]
        | Array<{ vehicle_id: VehicleId } | { group_id: VehicleGroupIdWithGPrefix }>
    }

export type DriverValuePromptTypeApiInput = {
  identifier: PromptKnownDriverTypeFieldName
  value: 'all' | DriverId | DriverGroupIdWithGPrefix
  type: PromptKnownDriverFieldType
}

export type StringValuePromptTypeApiInput = {
  identifier: PromptKnownStringTypeFieldName
  value: string
  type: PromptKnownStringFieldType
}

export type NumberValuePromptTypeApiInput = {
  identifier: PromptKnownNumOrNumberTypeFieldName
  value: string
  type: PromptKnownNumberFieldType
}

export type TimeValuePromptTypeApiInput = {
  identifier: PromptKnownTimeTypeFieldName
  value: string
  type: PromptKnownTimeFieldType
}

export type GeofenceValuePromptTypeApiInput = {
  identifier: PromptKnownGeofenceTypeFieldName
  value: string
  type: PromptKnownGeofenceFieldType
}

export type GeofenceGroupValuePromptTypeApiInput = {
  identifier: PromptKnownGeofenceGroupTypeFieldName
  value: string
  type: PromptKnownGeofenceGroupFieldType
}

export type SingleSelectValuePromptTypeApiInput = {
  identifier: PromptKnownSingleSelectTypeFieldName
  value: string
  type: PromptKnownSingleSelectFieldType
}

export type DayOfWeekValuePromptTypeApiInput = {
  identifier: PromptKnownDayOfWeekTypeFieldName
  value: string
  type: PromptKnownDayOfWeekFieldType
}

export type ReportPromptValueTypeApiInput =
  | RegistrationValuePromptTypeApiInput
  | DriverValuePromptTypeApiInput
  | GeofenceValuePromptTypeApiInput
  | GeofenceGroupValuePromptTypeApiInput
  | TimeValuePromptTypeApiInput
  | StringValuePromptTypeApiInput
  | NumberValuePromptTypeApiInput
  | SingleSelectValuePromptTypeApiInput
  | DayOfWeekValuePromptTypeApiInput
  | ({
      identifier: string
      value: string
      type: ExcludeStrict<
        PromptKnownFieldType,
        | RegistrationValuePromptTypeApiInput['type']
        | DriverValuePromptTypeApiInput['type']
        | GeofenceValuePromptTypeApiInput['type']
        | GeofenceGroupValuePromptTypeApiInput['type']
        | TimeValuePromptTypeApiInput['type']
        | StringValuePromptTypeApiInput['type']
        | NumberValuePromptTypeApiInput['type']
        | SingleSelectValuePromptTypeApiInput['type']
        | DayOfWeekValuePromptTypeApiInput['type']
      >
    } & Record<string, FixMeAny>)

export type ScheduledReportPromptValueType =
  | ExcludeStrict<ReportPromptValueTypeApiInput, RegistrationValuePromptTypeApiInput>
  | {
      identifier: PromptKnownVehicleTypeFieldName
      value: string
      type: PromptKnownVehicleFieldType
    }

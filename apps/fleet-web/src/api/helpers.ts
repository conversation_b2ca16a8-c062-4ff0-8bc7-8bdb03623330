import {
  enqueueSnackbarWithCloseAction,
  type EnqueueSnackbarWithCloseAction,
} from 'src/components/Snackbar/Notistack/utils'
import type { GlobalQueryMeta } from 'src/RouterRoot'
import { ctIntl } from 'src/util-components/ctIntl'
import { ctToast } from 'src/util-components/ctToast'

/**
 * @deprecated Use makeMutationErrorHandlerWithSnackbar instead
 */
export function makeMutationErrorHandlerWithToast(params?: {
  customErrorMessage?: string
  nonStandardErrorHandler?: (nonStandardError: unknown) => Promise<unknown> | void
}) {
  return {
    onError(error: unknown | Error): Promise<unknown> | void {
      if (error instanceof Error) {
        ctToast.fire('error', params?.customErrorMessage ?? error.message)
        if (ENV.NODE_ENV === 'development') {
          // So that we can debug errors properly in localhost
          console.error(error)
        }
        return
      }

      return params?.nonStandardErrorHandler?.(error)
    },
  }
}

export function makeQueryErrorHandlerWithToast<
  ExtraMeta extends Record<string, unknown>,
>(params?: {
  customErrorMessage?: string
  nonStandardErrorHandler?: (nonStandardError: unknown) => void
  extraMeta?: ExtraMeta
}) {
  const baseMeta = {
    metaErrorHandler: (error: unknown | Error): void => {
      if (error instanceof Error) {
        ctToast.fire('error', params?.customErrorMessage ?? error.message)
        if (ENV.NODE_ENV === 'development') {
          // So that we can debug errors properly in localhost
          console.error(error)
        }
        return
      }

      return params?.nonStandardErrorHandler?.(error)
    },
  } satisfies Pick<GlobalQueryMeta, 'metaErrorHandler'>

  return {
    meta: {
      ...baseMeta,
      ...params?.extraMeta,
    },
  }
}

export type NonStandardErrorHandler = (
  nonStandardError: unknown,
  {
    enqueueSnackbarWithCloseAction,
    showDefaultErrorSnackbar,
  }: {
    enqueueSnackbarWithCloseAction: EnqueueSnackbarWithCloseAction
    showDefaultErrorSnackbar: () => void
  },
) => Promise<unknown> | void
export function makeMutationErrorHandlerWithSnackbar(params?: {
  customErrorMessage?: string
  nonStandardErrorHandler?: NonStandardErrorHandler
}) {
  return {
    onError(error: unknown | Error): Promise<unknown> | void {
      if (error instanceof Error) {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({ id: params?.customErrorMessage ?? error.message }),
          { variant: 'error' },
        )
        if (ENV.NODE_ENV === 'development') {
          // So that we can debug errors properly in localhost
          console.error(error)
        }
        return
      }

      const showDefaultErrorSnackbar = () =>
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({ id: 'We are experiencing a technical error.' }),
          { variant: 'error' },
        )

      return params?.nonStandardErrorHandler?.(error, {
        enqueueSnackbarWithCloseAction,
        showDefaultErrorSnackbar,
      })
    },
  }
}

export function useQueryErrorHandlerWithSnackbar<
  ExtraMeta extends Record<string, unknown>,
>(params?: {
  customErrorMessage?: string
  nonStandardErrorHandler?: (
    nonStandardError: unknown,
    {
      enqueueSnackbarWithCloseAction,
    }: { enqueueSnackbarWithCloseAction: EnqueueSnackbarWithCloseAction },
  ) => void
  extraMeta?: ExtraMeta
}) {
  const baseMeta: Pick<GlobalQueryMeta, 'metaErrorHandler'> = {
    metaErrorHandler: (error: unknown | Error): void => {
      if (error instanceof Error) {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({ id: params?.customErrorMessage ?? error.message }),
          { variant: 'error' },
        )
        return
      }

      return params?.nonStandardErrorHandler?.(error, {
        enqueueSnackbarWithCloseAction,
      })
    },
  }

  return {
    meta: {
      ...baseMeta,
      ...params?.extraMeta,
    },
  }
}

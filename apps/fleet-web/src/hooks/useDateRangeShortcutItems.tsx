import { useMemo } from 'react'
import type { DateRange, PickersShortcutsItem } from '@karoo-ui/core'
import { DateTime } from 'luxon'

import type { CalendarShortcutId } from 'api/types'
import { ctIntl } from 'src/util-components/ctIntl'

export function useDateRangeShortcutItems({
  todayTimeStrategy = 'endOfDay',
}: {
  todayTimeStrategy?: 'now' | 'endOfDay'
} = {}) {
  return useMemo(() => {
    const getTodayEnd = () =>
      todayTimeStrategy === 'endOfDay'
        ? DateTime.local().endOf('day')
        : DateTime.local()

    return {
      today: {
        id: 'today' as CalendarShortcutId,
        label: ctIntl.formatMessage({ id: 'Today' }),
        getValue: () => {
          const todayEnd = getTodayEnd()
          return [todayEnd.startOf('day'), todayEnd]
        },
      },
      last7Days: {
        id: 'last7Days' as CalendarShortcutId,
        label: ctIntl.formatMessage(
          { id: 'dateRangePicker.shortcutItem.lastXDays' },
          { values: { count: 7 } },
        ),
        getValue: () => {
          const todayEnd = getTodayEnd()
          const start = todayEnd.minus({ days: 7 - 1 }).startOf('day')
          return [start, todayEnd]
        },
      },
      last15Days: {
        id: 'last15Days' as CalendarShortcutId,
        label: ctIntl.formatMessage(
          { id: 'dateRangePicker.shortcutItem.lastXDays' },
          { values: { count: 15 } },
        ),
        getValue: () => {
          const todayEnd = getTodayEnd()
          const start = todayEnd.minus({ days: 15 - 1 }).startOf('day')
          return [start, todayEnd]
        },
      },
      last30Days: {
        id: 'last30Days' as CalendarShortcutId,
        label: ctIntl.formatMessage(
          { id: 'dateRangePicker.shortcutItem.lastXDays' },
          { values: { count: 30 } },
        ),
        getValue: () => {
          const todayEnd = getTodayEnd()
          const start = todayEnd.minus({ days: 30 - 1 }).startOf('day')
          return [start, todayEnd]
        },
      },
      last60Days: {
        id: 'last60Days' as CalendarShortcutId,
        label: ctIntl.formatMessage(
          { id: 'dateRangePicker.shortcutItem.lastXDays' },
          { values: { count: 60 } },
        ),
        getValue: () => {
          const todayEnd = getTodayEnd()
          const start = todayEnd.minus({ days: 60 - 1 }).startOf('day')
          return [start, todayEnd]
        },
      },
      last90Days: {
        id: 'last90Days' as CalendarShortcutId,
        label: ctIntl.formatMessage(
          { id: 'dateRangePicker.shortcutItem.lastXDays' },
          { values: { count: 90 } },
        ),
        getValue: () => {
          const todayEnd = getTodayEnd()
          const start = todayEnd.minus({ days: 90 - 1 }).startOf('day')
          return [start, todayEnd]
        },
      },
      last120Days: {
        id: 'last120Days' as CalendarShortcutId,
        label: ctIntl.formatMessage(
          { id: 'dateRangePicker.shortcutItem.lastXDays' },
          { values: { count: 120 } },
        ),
        getValue: () => {
          const todayEnd = getTodayEnd()
          const start = todayEnd.minus({ days: 120 - 1 }).startOf('day')
          return [start, todayEnd]
        },
      },
      thisWeek: {
        id: 'thisWeek' as CalendarShortcutId,
        label: ctIntl.formatMessage({ id: 'dateRangePicker.shortcutItem.thisWeek' }),
        getValue: () => {
          const todayEnd = getTodayEnd()
          return [todayEnd.startOf('week'), todayEnd]
        },
      },
      lastWeek: {
        id: 'lastWeek' as CalendarShortcutId,
        label: ctIntl.formatMessage({
          id: 'dateRangePicker.shortcutItem.lastWeek',
        }),
        getValue: () => {
          const prevWeek = DateTime.local().minus({ weeks: 1 })
          return [prevWeek.startOf('week'), prevWeek.endOf('week')]
        },
      },
      last2Weeks: {
        id: 'last2Weeks' as CalendarShortcutId,
        label: ctIntl.formatMessage({
          id: 'dateRangePicker.shortcutItem.last2Weeks',
        }),
        getValue: () => {
          const now = DateTime.local()
          const prevWeek = now.minus({ weeks: 1 })
          const prev2Weeks = now.minus({ weeks: 2 })
          return [prev2Weeks.startOf('week'), prevWeek.endOf('week')]
        },
      },
      lastWeekTillDate: {
        id: 'lastWeekTillDate' as CalendarShortcutId,
        label: ctIntl.formatMessage({
          id: 'dateRangePicker.shortcutItem.lastWeekTillDate',
        }),
        getValue: () => {
          const todayEnd = getTodayEnd()
          const prevWeek = todayEnd.minus({ weeks: 1 })
          return [prevWeek.startOf('week'), todayEnd]
        },
      },
      thisMonth: {
        id: 'thisMonth' as CalendarShortcutId,
        label: ctIntl.formatMessage({ id: 'dateRangePicker.shortcutItem.thisMonth' }),
        getValue: () => {
          const todayEnd = getTodayEnd()
          return [todayEnd.startOf('month'), todayEnd]
        },
      },
      thisQuarter: {
        id: 'thisQuarter' as CalendarShortcutId,
        label: ctIntl.formatMessage({ id: 'dateRangePicker.shortcutItem.thisQuarter' }),
        getValue: () => {
          const todayEnd = getTodayEnd()
          return [todayEnd.startOf('quarter'), todayEnd]
        },
      },
      lastMonthTillDate: {
        id: 'lastMonthTillDate' as CalendarShortcutId,
        label: ctIntl.formatMessage({
          id: 'dateRangePicker.shortcutItem.lastMonthTillDate',
        }),
        getValue: () => {
          const todayEnd = getTodayEnd()
          const lastMonth = todayEnd.minus({ months: 1 })
          return [lastMonth.startOf('month'), todayEnd]
        },
      },
      lastMonth: {
        id: 'lastMonth' as CalendarShortcutId,
        label: ctIntl.formatMessage({ id: 'dateRangePicker.shortcutItem.lastMonth' }),
        getValue: () => {
          const lastMonth = DateTime.local().minus({ months: 1 })
          return [lastMonth.startOf('month'), lastMonth.endOf('month')]
        },
      },
      thisYear: {
        id: 'thisYear' as CalendarShortcutId,
        label: ctIntl.formatMessage({ id: 'dateRangePicker.shortcutItem.thisYear' }),
        getValue: () => {
          const todayEnd = getTodayEnd()
          return [todayEnd.startOf('year'), todayEnd]
        },
      },
      next7Days: {
        id: 'next7Days' as CalendarShortcutId,
        label: ctIntl.formatMessage(
          { id: 'dateRangePicker.shortcutItem.nextXDays' },
          { values: { count: 7 } },
        ),
        getValue: () => {
          const todayEnd = getTodayEnd()
          return [todayEnd, todayEnd.plus({ days: 7 - 1 })]
        },
      },
      reset: {
        id: 'reset' as CalendarShortcutId,
        label: ctIntl.formatMessage({ id: 'dateRangePicker.shortcutItem.reset' }),
        getValue: () => [null, null],
      },
    } as const satisfies Record<
      string,
      PickersShortcutsItem<DateRange<DateTime>> & {
        id: CalendarShortcutId
      }
    >
  }, [todayTimeStrategy])
}
